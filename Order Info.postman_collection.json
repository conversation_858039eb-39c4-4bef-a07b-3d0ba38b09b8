{"info": {"_postman_id": "f06d44df-ca09-4228-aeba-7215fee64a54", "name": "Order Info", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get Order Info", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.eyJpc3MiOiJiZXN0c2VsbGVyLWFjYyIsInN1YiI6Im9yZGVyLWluZm8tYXBpLXVzZXItemVuZGVzay1hY2MiLCJhdWQiOiJ6ZW5kZXNrLWFjYyIsImV4cCI6MTY2MTI0NTcwMX0.VI9eKaZAK8e8ss-Yt_KyZ-UZ6RwjgA_3DqSMNuLEslkFmre5ggSBl7aeDS7MJzclS-wzysEi5n6BKBMi1GCr7GP9m8WZwxhiNNnsFgNll_CiJF-qDoGa_tRt-zIxINnzop2qaG_z4WWU8AMgI2necJgo5ixHUD-n-NAuGyfVCcEgJosJ1-spD5nwkwQbm98knBLfO9dSbr_yZ2Q5mYDpLmrZvQxcwoWnneTgJOUBj1csiHqSdULd6hDNoRKNkDn9vzAu1zkWNuS0F6UudTQqTvP5jwuCJcq024Ld4yIFtIZbsEsBeXLyweh4kIGhfgyjGukY8wge27DSasM948BdCA", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "https://orders.{{env}}.bseint.io/9300066901", "protocol": "https", "host": ["orders", "{{env}}", "bseint", "io"], "path": ["9300066901"]}}, "response": []}, {"name": "<PERSON><PERSON>", "protocolProfileBehavior": {"disabledSystemHeaders": {}}, "request": {"auth": {"type": "apikey", "apikey": [{"key": "value", "value": "api key", "type": "string"}, {"key": "key", "value": "x-api-key", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "https://orders.{{env}}.bseint.io/login", "protocol": "https", "host": ["orders", "{{env}}", "bseint", "io"], "path": ["login"]}}, "response": []}]}

name: Deploy Receive ParcelLab Events function
on:
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        description: Select Environment to deploy
        options:
          - acc
          - prod
        required: true

jobs:
  DeployAcc:
    if: github.event.inputs.environment == 'acc'
    secrets: inherit
    uses: bestseller-ecom/bestseller-pipelines/.github/workflows/deploy-lambda.yml@v1
    with:
      environment: acc
      function-name: receive-parcellab-events
      project-name: logistic_lambdas

  DeployProd:
    secrets: inherit
    if: github.event.inputs.environment == 'prod' && github.ref == 'refs/heads/master'
    uses: bestseller-ecom/bestseller-pipelines/.github/workflows/deploy-lambda.yml@v2
    with:
      environment: prod
      function-name: receive-parcellab-events
      project-name: logistic_lambdas

name: Build
on:
  push:
    branches:
      - master
    paths-ignore:
      - 'infra/**'
      - 'README.md'
      - '**/README.md'
  workflow_dispatch:
    inputs:
      lambda-code-folder:
        type: choice
        options:
          - file-bundle-generator
          - order-info-api
          - order-info-api-user-authorizer
          - sfs-authorizer
          - post-purchase-authorizer
          - receive-fft-events
          - receive-parcellab-events
        description: 'Type the folder name with the code that you want to build from.'
        required: false
  pull_request:
    branches:
      - master
    paths-ignore:
      - 'infra/**'
      - 'README.md'
      - '**/README.md'

jobs:
  changed-folders:
    runs-on: ubuntu-latest
    outputs:
      changed: ${{ steps.get-changed.outputs.all_changed_files }}
    steps:
      - name: Git clone the repository
        uses: actions/checkout@v3

      - name: Verify Changed files
        uses: tj-actions/changed-files@v34
        id: get-changed

      - name: Echo changed files
        run: |
          for file in ${{ steps.get-changed.outputs.all_changed_files }}; do
            echo "$file was changed"
          done

  file-bundle-generator:
    needs: [ "changed-folders" ]
    if: contains(needs.changed-folders.outputs.changed, 'file-bundle-generator') || inputs.lambda-code-folder == 'file-bundle-generator'
    secrets: inherit
    uses: bestseller-ecom/bestseller-pipelines/.github/workflows/build-java-lambda.yml@v2
    with:
      working-dir: file-bundle-generator
      project-name: logistic_lambdas
      function-name: file-bundle-generator
      java-version: 21
      java-distribution: corretto
      build-tool: maven
      custom-artifact-name: file-bundle-generator-1.0-SNAPSHOT.jar

  order-info-api:
    needs: [ "changed-folders" ]
    if: contains(needs.changed-folders.outputs.changed, 'order-info-api') || inputs.lambda-code-folder == 'order-info-api'
    secrets: inherit
    uses: bestseller-ecom/bestseller-pipelines/.github/workflows/build-java-lambda.yml@v2
    with:
      working-dir: order-info-api
      project-name: logistic_lambdas
      function-name: order-info-api
      java-version: 21
      java-distribution: corretto
      build-tool: maven

  order-info-api-user-authorizer:
    needs: [ "changed-folders" ]
    if: contains(needs.changed-folders.outputs.changed, 'order-info-api-user-authorizer') || inputs.lambda-code-folder == 'order-info-api-user-authorizer'
    secrets: inherit
    uses: bestseller-ecom/bestseller-pipelines/.github/workflows/build-java-lambda.yml@v2
    with:
      working-dir: order-info-api-user-authorizer
      project-name: logistic_lambdas
      function-name: order-info-api-user-authorizer
      java-version: 21
      java-distribution: corretto
      build-tool: maven
      custom-artifact-name: order-info-api-user-authorizer-1.0-SNAPSHOT.jar
  
  sfs-authorizer:
    needs: [ "changed-folders" ]
    if: contains(needs.changed-folders.outputs.changed, 'authorizer') || inputs.lambda-code-folder == 'sfs-authorizer'
    secrets: inherit
    uses: bestseller-ecom/bestseller-pipelines/.github/workflows/build-python-lambda.yml@v1
    with:
      working-dir: authorizer
      project-name: logistic_lambdas
      function-name: sfs-authorizer
      python-version: 3.9
      files-to-include: authorizer.py,requirements.txt,test_authorizer.py
      enable-tests: true
      enable-docs-tests: true
      test-command: python -m unittest

  post-purchase-authorizer:
    needs: [ "changed-folders" ]
    if: contains(needs.changed-folders.outputs.changed, 'authorizer') || inputs.lambda-code-folder == 'post-purchase-authorizer'
    secrets: inherit
    uses: bestseller-ecom/bestseller-pipelines/.github/workflows/build-python-lambda.yml@v1
    with:
      working-dir: authorizer
      project-name: logistic_lambdas
      function-name: post-purchase-authorizer
      python-version: 3.9
      files-to-include: authorizer.py,requirements.txt,test_authorizer.py
      enable-tests: true
      enable-docs-tests: true
      test-command: python -m unittest

  receive-fft-events:
    needs: [ "changed-folders" ]
    if: contains(needs.changed-folders.outputs.changed, 'receive-webhook-events') || inputs.lambda-code-folder == 'receive-fft-events'
    secrets: inherit
    uses: bestseller-ecom/bestseller-pipelines/.github/workflows/build-python-lambda.yml@v1
    with:
      working-dir: receive-webhook-events
      project-name: logistic_lambdas
      function-name: receive-fft-events
      python-version: 3.9
      files-to-include: lambda_function.py,requirements.txt,test_lambda_function.py
      enable-tests: true
      enable-docs-tests: true
      test-command: python -m unittest
  receive-parcellab-events:
    needs: [ "changed-folders" ]
    if: contains(needs.changed-folders.outputs.changed, 'receive-webhook-events') || inputs.lambda-code-folder == 'receive-parcellab-events'
    secrets: inherit
    uses: bestseller-ecom/bestseller-pipelines/.github/workflows/build-python-lambda.yml@v1
    with:
      working-dir: receive-webhook-events
      project-name: logistic_lambdas
      function-name: receive-parcellab-events
      python-version: 3.9
      files-to-include: lambda_function.py,requirements.txt,test_lambda_function.py
      enable-tests: true
      enable-docs-tests: true
      test-command: python -m unittest
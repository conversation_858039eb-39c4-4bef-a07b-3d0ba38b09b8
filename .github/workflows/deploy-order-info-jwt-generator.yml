name: Deploy order-info-jwt-generator lambda
on:
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        description: Select Environment to deploy
        options:
          - acc
          - prod
        required: true

jobs:

  deploy-acc:
    if: github.event.inputs.environment == 'acc'
    secrets: inherit
    uses: bestseller-ecom/bestseller-pipelines/.github/workflows/deploy-lambda.yml@v2
    with:
      environment: ${{ inputs.environment }}
      function-name: order-info-jwt-generator
      project-name: logistic_lambdas
      custom-s3-key: "logistic_lambdas/order-info-api-user-authorizer/order-info-api-user-authorizer-${{ github.sha }}.jar"

  deploy-prod:
    if: github.event.inputs.environment == 'prod' && github.ref == 'refs/heads/master'
    secrets: inherit
    uses: bestseller-ecom/bestseller-pipelines/.github/workflows/deploy-lambda.yml@v2
    with:
      environment: ${{ inputs.environment }}
      function-name: order-info-jwt-generator
      project-name: logistic_lambdas
      custom-s3-key: "logistic_lambdas/order-info-api-user-authorizer/order-info-api-user-authorizer-${{ github.sha }}.jar"

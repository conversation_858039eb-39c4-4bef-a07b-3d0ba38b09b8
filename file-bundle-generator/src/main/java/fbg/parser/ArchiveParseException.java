package fbg.parser;

/**
 * Wrapper for exception thrown during the parsing of the archive.
 */
public class ArchiveParseException extends RuntimeException {
    public ArchiveParseException(Exception e) {
        super(e);
    }

    public ArchiveParseException(String message) {
        super(message);
    }

    public ArchiveParseException(String message, Throwable cause) {
        super(message, cause);
    }
}

package fbg.parser.ingrammicro.returns;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Data class that represents the returns from ingram_micro, received in OSIMC.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReturnsRequest {
    private String orderCode;

    /**
     * Get order code.
     * @return
     */
    public String getOrderCode() {
        return orderCode;
    }
}

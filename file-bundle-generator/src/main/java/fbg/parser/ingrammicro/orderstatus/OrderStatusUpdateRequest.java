package fbg.parser.ingrammicro.orderstatus;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Data class that represents the orderStatusUpdates from ingram_micro, received in OSIMC.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrderStatusUpdateRequest {
    private String orderCode;

    /**
     * Get order code.
     * @return
     */
    public String getOrderCode() {
        return orderCode;
    }
}

package fbg.parser.tradebyte;

import fbg.configuration.FileType;
import fbg.parser.XmlSingleTagParser;

/**
 * Parser for tradebyte orders status.
 */
public class TradebyteOrderStatusExportParser extends XmlSingleTagParser {
    private static final String ORDER_DATA_TAG = "TB_ORDER_ID";

    @Override
    protected String extractOrderId(String orderId) {
        return "TB" + orderId;
    }

    @Override
    protected String getTag() {
        return ORDER_DATA_TAG;
    }

    @Override
    public FileType getArchiveType() {
        return FileType.ORDER_STATUS;
    }
}

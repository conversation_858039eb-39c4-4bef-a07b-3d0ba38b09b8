package fbg.parser.hermes;

import com.amazonaws.services.s3.model.S3Object;

import java.util.Optional;
import java.util.Set;
import java.util.function.Function;

/**
 * Base parse for the hermes file fakt and pod files.
 */
public abstract class HermesOrderUpdateParser extends HermesBaseTsvParser {

    /**
     * Order line handler for the order updates.
     */
    private final Function<String, Optional<String>> orderUpdateLineHandler = (String line) -> {
        Optional<String> optional = Optional.empty();
        int index = line.indexOf(FIELD_SEPARATOR);
        if (index > 0) {
            optional = Optional.of(line.substring(0, index));
        }
        return optional;
    };

    @Override
    public Set<String> parse(S3Object s3Object) {
        return super.parse(s3Object, orderUpdateLineHandler);
    }
}

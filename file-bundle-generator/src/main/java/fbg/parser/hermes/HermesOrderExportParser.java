package fbg.parser.hermes;

import fbg.configuration.FileType;
import fbg.parser.XmlSingleTagParser;

/**
 * Parser for order export.
 */
public class HermesOrderExportParser extends XmlSingleTagParser {
    private static final String S_EXTERN_ORDERNO = "s_extern_orderno";

    @Override
    protected String extractOrderId(String orderId) {
        return orderId;
    }

    @Override
    protected String getTag() {
        return S_EXTERN_ORDERNO;
    }

    @Override
    public FileType getArchiveType() {
        return FileType.ORDER_EXPORT;
    }
}

package fbg.parser.hermes;

import com.amazonaws.services.s3.model.S3Object;
import fbg.configuration.FileType;

import java.util.Optional;
import java.util.Set;
import java.util.function.Function;

/**
 * Parser for the hermes return files.
 */
public class HermesOrderReturnParser extends HermesBaseTsvParser {
    private static final int ORDER_ID_FIELD_INDEX = 23;

    private final Function<String, Optional<String>> returnLineHandler = (String line) -> {
        Optional<String> optional = Optional.empty();
        String[] fields = line.split(FIELD_SEPARATOR);
        if (fields.length > ORDER_ID_FIELD_INDEX) {
            optional = Optional.ofNullable(fields[ORDER_ID_FIELD_INDEX]);
        }
        return optional;
    };

    @Override
    public Set<String> parse(S3Object s3Object) {
        return super.parse(s3Object, returnLineHandler);
    }

    @Override
    public FileType getArchiveType() {
        return FileType.RETURN;
    }
}

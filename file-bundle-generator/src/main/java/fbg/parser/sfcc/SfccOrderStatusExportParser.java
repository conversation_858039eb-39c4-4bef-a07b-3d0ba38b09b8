package fbg.parser.sfcc;

import fbg.configuration.FileType;
import fbg.parser.XmlSingleTagParser;

/**
 * Parser for sfcc orders status.
 */
public class SfccOrderStatusExportParser extends XmlSingleTagParser {
    private static final String ORDER_TAG = "orderId";

    @Override
    protected String extractOrderId(String orderId) {
        return orderId;
    }

    @Override
    protected String getTag() {
        return ORDER_TAG;
    }

    @Override
    public FileType getArchiveType() {
        return FileType.ORDER_STATUS;
    }
}

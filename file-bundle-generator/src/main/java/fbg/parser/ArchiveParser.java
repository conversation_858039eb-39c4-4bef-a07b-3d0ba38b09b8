package fbg.parser;

import com.amazonaws.services.s3.model.S3Object;
import fbg.configuration.FileType;

import java.util.Set;

/**
 * Interface that the various archive parses needs to implement.
 * A parser needs to retrieve the order ids and the type of archive.
 */
public interface ArchiveParser {

    /**
     * Parse an s3Object and retrieves the order ids.
     * @param s3Object
     * @return
     */
    Set<String> parse(S3Object s3Object);

    /**
     * Return the type of archive(e.g. order import, fakt, pod, returns ..).
     * @return
     */
    FileType getArchiveType();

}

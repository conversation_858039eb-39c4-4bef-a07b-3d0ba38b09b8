package fbg.context;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import fbg.parser.ArchiveParserFactory;
import fbg.service.*;

/**
 * Class responsible for beans creation.
 */
public final class BeanFactory {
    private static BeanFactory instance = new BeanFactory();

    private AmazonS3 amazonS3;
    private ElasticSearchService elasticSearchService;
    private S3Service s3Service;
    private ArchiveProcessService archiveProcessService;
    private ArchiveParserFactory archiveParserFactory;
    private ZipCreator zipCreator;

    private BeanFactory() {

    }

    public static BeanFactory getInstance() {
        return instance;
    }

    /**
     * Returns an AmazonS3 client.
     */
    public AmazonS3 getAmazonS3() {
        if (amazonS3 == null) {
            amazonS3 = AmazonS3ClientBuilder.standard().build();
        }
        return amazonS3;
    }

    /**
     * @return ElasticSearchService.
     */
    public ElasticSearchService getElasticSearchService() {
        if (elasticSearchService == null) {
            elasticSearchService = new ElasticSearchService();
        }
        return elasticSearchService;
    }

    /**
     * @return S3Service.
     */
    public S3Service getS3Service() {
        if (s3Service == null) {
            s3Service = new S3Service();
        }
        return s3Service;
    }

    /**
     * @return ArchiveProcessService.
     */
    public ArchiveProcessService getArchiveProcessService() {
        if (archiveProcessService == null) {
            archiveProcessService = new ArchiveProcessService();
        }
        return archiveProcessService;
    }

    /**
     * @return ArchiveParserFactory.
     */
    public ArchiveParserFactory getArchiveParserFactory() {
        if (archiveParserFactory == null) {
            archiveParserFactory = new ArchiveParserFactory();
        }
        return archiveParserFactory;
    }

    /**
     * @return ZipCreator.
     */
    public ZipCreator getZipCreator() {
        if (zipCreator == null) {
            zipCreator = new ZipCreator();
        }
        return zipCreator;
    }

}

package fbg.configuration;

public class S3OrangeFileConfig {
    private static S3OrangeFileConfig instance = new S3OrangeFileConfig();
    private static final long ARCHIVE_RETENTION_MS = 6 * 3600 * 1000L;

    private String bucketName = System.getenv("S3_ARCHIVE_ORANGES_BUCKET");

    private S3OrangeFileConfig() {
    }

    public static S3OrangeFileConfig getInstance() {
        return instance;
    }

    public String getBucketName() {
        return bucketName;
    }

    public long getArchiveRetentionMs() {
        return ARCHIVE_RETENTION_MS;
    }
}

package fbg.configuration;

/**
 * HWC_S3_PROCESSED_BUCKET
 * Look at hwc.archive.S3KeyPrefix
 * bse-hwc-archive-prod.bseint.io .
 */
public final class HwcConfiguration {
    /**
     * fakt folder in the hwc archive.
     */
    public static final String POD_FOLDER = "pod";
    /**
     * pod folder in the hwc archive.
     */
    public static final String FAKT_FOLDER = "fakt";
    /**
     * return folder in the hwc archive.
     */
    public static final String RETURNS_FOLDER = "returns";
    /**
     * order export folder in the hwc archive.
     */
    public static final String ORDER_EXPORT_FOLDER = "export";

    private static HwcConfiguration instance = new HwcConfiguration();

    private String bucketName = System.getenv("S3_HWC_BUCKET_NAME");

    private HwcConfiguration() {
    }

    public static HwcConfiguration getInstance() {
        return instance;
    }

    public String getBucketName() {
        return bucketName;
    }
}

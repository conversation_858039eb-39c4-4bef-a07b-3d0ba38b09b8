package fbg.configuration;

import org.apache.commons.lang.StringUtils;

import java.util.Optional;

/**
 * Bundle directory configuration.
 * Determines the directory in the order bundle where a particular file will be placed.
 * It is a function of the {@link FileType}, the S3 bucket name and the directory in that bucket where that file came from.
 */
public enum BundleDirectoryConfiguration {

    /**
     * Orders coming in from Sales Force Commerce Cloud.
     */
    ORDER_IMPORT_SFCC(FileType.ORDER, OmsConfiguration.getInstance().getBucketName(), OmsConfiguration.SFCC_ORDER_FOLDER),
    /**
     * Orders coming in from TradeByte.
     */
    ORDER_IMPORT_TB(FileType.ORDER, TbcConfiguration.getInstance().getBucketName(), TbcConfiguration.TRADEBYTE_ORDER_FOLDER),
    /**
     * Orders exported to Hermes warehouse.
     */
    ORDER_EXPORT_HERMES(FileType.ORDER_EXPORT, HwcConfiguration.getInstance().getBucketName(), HwcConfiguration.ORDER_EXPORT_FOLDER),
    /**
     * Orders exported to Ingram Micro warehouse.
     */
    ORDER_EXPORT_IM(FileType.ORDER_EXPORT, ImwcConfiguration.getInstance().getBucketName(), null),
    /**
     * Order acknowledgements / cancellations coming in from Hermes warehouse.
     */
    ORDER_STATUS_IMPORT_FAKT_HERMES(FileType.FAKT, HwcConfiguration.getInstance().getBucketName(), HwcConfiguration.FAKT_FOLDER),
    /**
     * Order acknowledgements / cancellations coming in from Ingram Micro warehouse.
     */
    ORDER_STATUS_IMPORT_FAKT_IM(FileType.FAKT, ImwcConfiguration.getInstance().getBucketName(), null),
    /**
     * Order dispatch confirmations coming in from Hermes warehouse.
     */
    ORDER_STATUS_IMPORT_POD_HERMES(FileType.POD, HwcConfiguration.getInstance().getBucketName(), HwcConfiguration.POD_FOLDER),
    /**
     * Order dispatch confirmations coming in from Ingram Micro warehouse.
     */
    ORDER_STATUS_IMPORT_POD_IM(FileType.POD, ImwcConfiguration.getInstance().getBucketName(), null),
    /**
     * Order return information coming in from Hermes warehouse.
     */
    ORDER_STATUS_IMPORT_RETURN_HERMES(FileType.RETURN, HwcConfiguration.getInstance().getBucketName(), HwcConfiguration.RETURNS_FOLDER),
    /**
     * Order return information coming in from Ingram Micro warehouse.
     */
    ORDER_STATUS_IMPORT_RETURN_IM(FileType.RETURN, ImwcConfiguration.getInstance().getBucketName(), null),
    /**
     * Order status updates exported to Sales Force Commerce Cloud.
     */
    ORDER_STATUS_EXPORT_SFCC(FileType.ORDER_STATUS, OmsConfiguration.getInstance().getBucketName(), OmsConfiguration.SFCC_ORDER_STATUS_FOLDER),
    /**
     * Order status updates exported to TradeByte.
     */
    ORDER_STATUS_EXPORT_TB(FileType.ORDER_STATUS, OmsConfiguration.getInstance().getBucketName(), OmsConfiguration.TB_ORDER_STATUS_FOLDER),
    /**
     * Order return information coming in from Ingram Micro warehouse via API.
     */
    ORDER_STATUS_RETURNS_IM(FileType.RETURN, OsimcConfiguration.getInstance().getBucketName(), OsimcConfiguration.RETURNS_BASE_FOLDER),
    /**
     * Order status updates coming in from Ingram Micro warehouse via API.
     */
    ORDER_STATUS_IM(FileType.ORDER_STATUS, OsimcConfiguration.getInstance().getBucketName(), OsimcConfiguration.ORDER_STATUS_BASE_FOLDER),
    /**
     * Payloads that are sent to the ParcelLab.
     */
    PARCELLAB_PAYLOADS_PLBC(FileType.PARCELLAB_PAYLOADS, PlbcConfiguration.getInstance().getBucketName(), PlbcConfiguration.PAYLOADS_FOLDER);

    /**
     * Directory path separator.
     */
    public static final String PATH_SEPARATOR = "/";
    /**
     * Directory for unknown files.
     */
    public static final String UNKNOWN_DIRECTORY = "unknown" + PATH_SEPARATOR;

    private FileType fileType;
    private String s3Bucket;
    private String parentPath;

    /**
     * Constructor.
     *
     * @param fileType   the {@link FileType}
     * @param s3Bucket   the S3 bucket
     * @param parentPath the parent path in bucket(can be null, means file is at root)
     */
    BundleDirectoryConfiguration(FileType fileType, String s3Bucket, String parentPath) {
        this.fileType = fileType;
        this.s3Bucket = s3Bucket;
        this.parentPath = parentPath;
    }

    /**
     * Get the correct {@link BundleDirectoryConfiguration} which is a function of the file type, the S3 bucket and directory in the S3 bucket.
     *
     * @param fileType   the file type
     * @param s3Bucket   the bucket name
     * @param parentPath the parent path in bucket(can be null, means file is at root)
     * @return the {@link Optional} {@link BundleDirectoryConfiguration}
     */
    public static Optional<BundleDirectoryConfiguration> getBundleDirectoryConfiguration(String fileType, String s3Bucket, String parentPath) {
        for (BundleDirectoryConfiguration bundleDirectoryConfiguration : values()) {
            if (bundleDirectoryConfiguration.fileType.name().equals(fileType)
                && bundleDirectoryConfiguration.s3Bucket.equals(s3Bucket)
                && StringUtils.equals(bundleDirectoryConfiguration.parentPath, parentPath)) {
                return Optional.of(bundleDirectoryConfiguration);
            }
        }
        return Optional.empty();
    }

    /**
     * Gets the name of the directory in the order bundle.
     *
     * @return the name of the directory
     */
    public String getBundleDirectory() {
        return this.name().toLowerCase() + PATH_SEPARATOR;
    }
}

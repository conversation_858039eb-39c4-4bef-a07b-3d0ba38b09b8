package fbg.configuration;

/**
 *  bse-tbc-archive-prod.bseint.io.
 */
public final class TbcConfiguration {

    /**
     * tradebyte order folder in the tbc archive.
     */
    public static final String TRADEBYTE_ORDER_FOLDER = "tb-order";

    private static TbcConfiguration instance = new TbcConfiguration();

    private String bucketName = System.getenv("S3_TBC_BUCKET_NAME");

    private TbcConfiguration() {
    }

    public static TbcConfiguration getInstance() {
        return instance;
    }

    public String getBucketName() {
        return bucketName;
    }
}

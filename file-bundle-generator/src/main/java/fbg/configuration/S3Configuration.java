package fbg.configuration;

/**
 * Configuration for S3 of FBG service.
 */
public final class S3Configuration {
    private static S3Configuration instance = new S3Configuration();
    private static final long ARCHIVE_RETENTION_MS = 6 * 3600 * 1000L;

    private String bucketName = System.getenv("S3_FBG_BUCKET_NAME");

    private S3Configuration() {
    }

    public static S3Configuration getInstance() {
        return instance;
    }

    public String getBucketName() {
        return bucketName;
    }

    public long getArchiveRetentionMs() {
        return ARCHIVE_RETENTION_MS;
    }
}

package fbg.configuration;

/**
 * S3_OSIMC_BUCKET_NAME
 * bse-osimc-archive-prod.bseint.io .
 */
public final class OsimcConfiguration {
    /**
     * Returns regex for the osimc RETURNS archive.
     * Example file returns/2020/9/24/ingram_micro_returns_20200924083604811.json
     */
    public static final String RETURNS_FILE_PREFIX = "returns";
    /**
     * Returns regex for the osimc ORDER_STATUS_UPDATE archive.
     * Example file order-statuses/2020/9/24/ingram_micro_status_update_20200924083604811.json
     */
    public static final String ORDER_STATUS_FILE_PREFIX = "order-statuses";

    /**
     * Returns folder in bucket.
     */
    public static final String RETURNS_BASE_FOLDER = RETURNS_FILE_PREFIX;
    /**
     * Order status update folder in bucket.
     */
    public static final String ORDER_STATUS_BASE_FOLDER = ORDER_STATUS_FILE_PREFIX;
    private static OsimcConfiguration instance = new OsimcConfiguration();
    private final String bucketName = System.getenv("S3_OSIMC_BUCKET_NAME");

    private OsimcConfiguration() {
    }

    public static OsimcConfiguration getInstance() {
        return instance;
    }

    public String getBucketName() {
        return this.bucketName;
    }
}

package fbg.configuration;

/**
 * S3_IMWC_BUCKET_NAME
 * bse-imwc-archive-prod.bseint.io .
 */
public final class ImwcConfiguration {
    /**
     * pod regex for the imwc archive.
     */
    public static final String POD_FILE_PREFIX = "t_orderstat_pod";
    /**
     * fakt regex for the imwc archive.
     */
    public static final String FAKT_FILE_PREFIX = "t_orderstat_fakt";
    /**
     * return regex for the imwc archive.
     */
    public static final String RETURNS_FILE_PREFIX = "t_returnsum";
    /**
     * order export regex for the imwc archive.
     */
    public static final String ORDER_EXPORT_PREFIX = "Order_Import_40_";

    private static ImwcConfiguration instance = new ImwcConfiguration();

    private String bucketName = System.getenv("S3_IMWC_BUCKET_NAME");

    private ImwcConfiguration() {
    }

    public static ImwcConfiguration getInstance() {
        return instance;
    }

    public String getBucketName() {
        return this.bucketName;
    }
}

package fbg.configuration;

/**
 * Configuration class for the Elastic Search.
 */
public final class ElasticSearchConfiguration {

    /**
     * Index used for elastic search.
     */
    public static final String DOCUMENT_INDEX = "files";
    /**
     * Type used for elastic search.
     */
    public static final String DOCUMENT_TYPE = "file";
    /**
     * Parameter used for elastic search.
     */
    public static final String SEARCH_PARAM = "orders";
    /**
     * Command used for elastic search.
     */
    public static final String SEARCH_COMMAND = "_search";

    /**
     * Count used for elastic search.
     */
    public static final String COUNT_COMMAND = "_count";

    /**
     * Size parameter for elastic search.
     */
    public static final String SIZE = "size";

    /**
     * Query indicator for elastic search.
     */
    public static final String QUERY = "q";

    private static ElasticSearchConfiguration instance = new ElasticSearchConfiguration();
    private String endpoint = System.getenv("ELASTIC_SEARCH_ENDPOINT");

    private ElasticSearchConfiguration() {
    }

    public static ElasticSearchConfiguration getInstance() {
        return instance;
    }

    public String getEndpoint() {
        return endpoint;
    }
}

package fbg.service;

import fbg.invoce.DownloadFile;

import java.io.*;
import java.util.Collection;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * Class to add entries to a zip archive.
 */
public class ZipCreator {
    private static final int BUFFER_SIZE = 1024;
    private byte[] buffer = new byte[BUFFER_SIZE];

    /**
     * Adds a new entry to the existing zipOutputStream.
     * @param zipOutputStream Existing zip archive
     * @param inputStream The content to add
     * @param filePath The path of the content within the archive
     * @throws IOException
     */
    public void addToArchive(ZipOutputStream zipOutputStream, InputStream inputStream, String filePath) throws IOException {
        zipOutputStream.putNextEntry(new ZipEntry(filePath));

        int len;
        while ((len = inputStream.read(buffer)) > 0) {
            zipOutputStream.write(buffer, 0, len);
        }
    }

    public byte[] bundleZip(Collection<DownloadFile> files) throws IOException {
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();

        // Bundle file contents into byte array, where each file exist with it's filename.
        try (ZipOutputStream zipStream = new ZipOutputStream(buffer)) {
            // Iterate over files Map and creating new file entry into ZipOutputStream for each file.
            for (final DownloadFile file : files) {
                zipStream.putNextEntry(new ZipEntry(file.getFileName()));
                zipStream.write(file.getContent());

                // Entry will close automatically.
            }
        }
        buffer.flush();
        // It's important to read buffer only *after* Zip stream is closed,
        return buffer.toByteArray();
    }
}

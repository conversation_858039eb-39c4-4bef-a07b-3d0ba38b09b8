{"$id": "http://example.com/example.json", "type": "object", "definitions": {}, "$schema": "http://json-schema.org/draft-06/schema#", "properties": {"hits": {"$id": "/properties/hits", "type": "object", "properties": {"total": {"$id": "/properties/hits/properties/total", "type": "integer", "title": "The Total Schema", "description": "An explanation about the purpose of this instance.", "default": 0, "examples": [1]}, "hits": {"$id": "/properties/hits/properties/hits", "type": "array", "items": {"$id": "/properties/hits/properties/hits/items", "type": "object", "properties": {"_source": {"$ref": "ArchiveContent.json"}}}}}}}}
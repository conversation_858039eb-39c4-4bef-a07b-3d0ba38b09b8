{"$id": "http://example.com/example.json", "type": "object", "definitions": {}, "$schema": "http://json-schema.org/draft-07/schema#", "properties": {"count": {"$id": "/properties/count", "type": "integer", "title": "The <PERSON> ", "default": 0, "examples": [0]}, "_shards": {"$id": "/properties/_shards", "type": "object", "properties": {"total": {"$id": "/properties/_shards/properties/total", "type": "integer", "title": "The Total Schema ", "default": 0}, "successful": {"$id": "/properties/_shards/properties/successful", "type": "integer", "title": "The Successful Schema ", "default": 0}, "skipped": {"$id": "/properties/_shards/properties/skipped", "type": "integer", "title": "The Skipped Schema ", "default": 0, "examples": [0]}, "failed": {"$id": "/properties/_shards/properties/failed", "type": "integer", "title": "The Failed Schema ", "default": 0, "examples": [0]}}}}}
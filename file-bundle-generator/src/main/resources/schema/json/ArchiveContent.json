{"definitions": {}, "$schema": "http://json-schema.org/draft-06/schema#", "$id": "fbg/ArchiveContent", "type": "object", "properties": {"filePath": {"$id": "/properties/filePath", "type": "string", "title": "The Filename Schema.", "description": "Full path to the file, including the folder.", "default": "", "examples": ["fakt_sample.asc", "returns/returnfile.asc"]}, "s3bucket": {"$id": "/properties/s3bucket", "type": "string", "title": "The S3bucket Schema.", "description": "The name of the S3 bucket", "default": "", "examples": ["sample_s3_bucket_name"]}, "type": {"$id": "/properties/type", "type": "string", "title": "The Type Schema.", "description": "The type of file", "default": "", "examples": ["FAKT"]}, "lastModified": {"$id": "/properties/lastModified", "type": "string", "title": "Last modified.", "description": "Last modified timestamp of the s3 object", "default": "", "format": "date-time"}, "orders": {"$id": "/properties/orders", "type": "array", "items": {"$id": "/properties/orders/items", "type": "string", "title": "The 0 Schema.", "description": "The order id", "default": "", "examples": ["1234AB"]}}}, "required": ["filename", "s3bucket", "type", "orders"]}
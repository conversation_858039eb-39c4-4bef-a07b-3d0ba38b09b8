Meta:
@issue TTS-1503

Narrative:
As a software engineer
I would like to verify that an arbitrary number of files can be retrieved
(larger than 10 which is the default value for elasticache query).

Lifecycle:
Before:
Given configuration is set up
And Buckets are empty
And files index does not exist

Scenario: Multiple archives of different types are uploaded to s3
          First lambda function is triggered for indexing
          And then second lambda function is triggered for downloading the bundle
Given multiple uploaded archives:
|fileNameKey|bucketKey|archiveType|                   orderIdKeys|
|     f1|      oms|       cc_order|                              cc_1|
|     f2|      hwc|   order_export|                              cc_1|
|     f3|      hwc|           fakt|                         cc_1,cc_2|
|     f4|      hwc|           fakt|                         cc_1,cc_2|
|     f5|      hwc|           fakt|cc_1,tb_2,tb_3,tb_4,tb_5,tb_6,tb_7|
|     f6|     imwc|   order_export|                              cc_1|
|     f7|     imwc|           fakt|                              cc_1|
|     f8|     imwc|           fakt|                         cc_1,cc_3|
|     f9|     imwc|           fakt|                         cc_1,cc_4|
|    f10|      hwc|           fakt|     cc_1,tb_1,tb_2,tb_3,tb_4,cc_3|
|    f11|     imwc|           fakt|cc_1,tb_3,tb_4,tb_5,tb_6,tb_7,cc_4|
|    f12|     imwc|           fakt|        cc_1,tb_8,tb_9,tb_10,tb_11|
|    f13|      hwc|           fakt|               cc_1,cc_2,tb_1,tb_2|
|    f14|     imwc|           fakt|          cc_1,tb_4,tb_5,tb_6,tb_7|
|    f15|     imwc|           fakt|                         cc_1,cc_5|
|    f16|      hwc|           fakt|                  cc_1,tb_12,tb_13|
|    f17|      hwc|           fakt|               cc_1,cc_5,tb_8,tb_9|
|    f18|     imwc|           fakt|       cc_1,cc_5,tb_10,tb_11,tb_12|
|    f19|      hwc|           fakt|               cc_1,cc_5,tb_8,tb_9|
|    f20|     imwc|           fakt|       cc_1,cc_5,tb_10,tb_11,tb_12|
|    f21|      hwc|           fakt|               cc_1,tb_3,tb_4,cc_3|
|    f22|     imwc|            pod|          cc_1,tb_4,tb_5,tb_6,tb_7|
|    f23|      hwc|            pod|                    cc_1,cc_2,tb_1|
|    f24|     imwc|         return|                    cc_1,tb_5,tb_7|
|    f25|     imwc|         return|                         cc_1,cc_6|
|    f26|      hwc|         return|                   cc_1,cc_6,tb_13|
|    f27|     imwc|         return|                        cc_1,tb_13|
|    f28|      oms|cc_order_status|                         cc_1,cc_6|
|    f29|      oms|tb_order_status|                   tb_5,tb_7,tb_13|
When S3 object creation notifications have been triggered
Then all the orders archives are indexed:
|orderIdKey|                                                                                      fileNameKeys|
|      cc_1|f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13,f14,f15,f16,f17,f18,f19,f20,f21,f22,f23,f24,f25,f26,f27,f28|
|      cc_2|                                                                                         f3,f4,f13,f23|
|      cc_3|                                                                                            f8,f10,f21|
|      cc_4|                                                                                                f9,f11|
|      cc_5|                                                                                   f15,f17,f18,f19,f20|
|      cc_6|                                                                                           f25,f26,f28|
|      tb_1|                                                                                           f10,f13,f23|
|      tb_2|                                                                                            f5,f10,f13|
|      tb_3|                                                                                        f5,f10,f11,f21|
|      tb_4|                                                                                f5,f10,f11,f14,f21,f22|
|      tb_5|                                                                                f5,f11,f14,f22,f24,f29|
|      tb_6|                                                                                        f5,f11,f14,f22|
|      tb_7|                                                                                f5,f11,f14,f22,f24,f29|
|      tb_8|                                                                                           f12,f17,f19|
|      tb_9|                                                                                           f12,f17,f19|
|     tb_10|                                                                                           f12,f18,f20|
|     tb_11|                                                                                           f12,f18,f20|
|     tb_12|                                                                                           f16,f18,f20|
|     tb_13|                                                                                       f16,f26,f27,f29|
When order bundle is downloaded for order cc_1
Then order bundle contains the files with the following directory structure :
|directoryInBundle                  |fileNameKeys                       |
|order_import_sfcc                  |f1                                 |
|order_export_hermes                |f2                                 |
|order_status_import_fakt_hermes    |f3,f4,f5,f10,f13,f16,f17,f19,f21   |
|order_export_im                    |f6                                 |
|order_status_import_fakt_im        |f7,f8,f9,f11,f12,f14,f15,f18,f20   |
|order_status_import_pod_im         |f22                                |
|order_status_import_pod_hermes     |f23                                |
|order_status_import_return_im      |f24,f25,f27                        |
|order_status_import_return_hermes  |f26                                |
|order_status_export_sfcc           |f28                                |

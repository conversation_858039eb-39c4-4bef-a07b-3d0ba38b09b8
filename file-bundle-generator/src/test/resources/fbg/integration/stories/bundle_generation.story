Meta:
@issue BDV-2363

Narrative:
As a software engineer
I would like to be able to index the content of the archived files, so that I have the needed sources for generating the bundles.
Acceptance criteria
An archival to S3 triggers an event
The lambda function is triggered by these events and indexes the file as described in BDV-2426

Lifecycle:
Before:
Given configuration is set up
And Buckets are empty
And files index does not exist

Scenario: Multiple archives of different types are uploaded to s3
          First lambda function is triggered for indexing
          And then second lambda function is triggered for downloading the bundle
Given multiple uploaded archives:
|      fileNameKey|bucketKey|    archiveType|                       orderIdKeys|
|   oms_cc_order_1|      oms|       cc_order|                              cc_1|
|   oms_cc_order_2|      oms|       cc_order|                              cc_2|
|    hwc_ord_exp_1|      hwc|   order_export|                         cc_1,cc_2|
|       hwc_fakt_1|      hwc|           fakt|                         cc_1,cc_2|
|   tb_order_1    |      tbc|       tb_order|tb_1,tb_2,tb_3,tb_4,tb_5,tb_6,tb_7|
|   imwc_ord_exp_1|     imwc|   order_export|                              cc_1|
|      imwc_fakt_1|     imwc|           fakt|                              cc_1|
|   oms_cc_order_3|      oms|       cc_order|                              cc_3|
|   oms_cc_order_4|      oms|       cc_order|                              cc_4|
|       hwc_fakt_2|      hwc|           fakt|          tb_1,tb_2,tb_3,tb_4,cc_3|
|      imwc_fakt_2|     imwc|           fakt|     tb_3,tb_4,tb_5,tb_6,tb_7,cc_4|
|       tb_order_2|      tbc|       tb_order|             tb_8,tb_9,tb_10,tb_11|
|        hwc_pod_1|      hwc|            pod|               cc_1,cc_2,tb_1,tb_2|
|       imwc_pod_1|     imwc|            pod|          cc_1,tb_4,tb_5,tb_6,tb_7|
|cc_order_status_1|      oms|cc_order_status|                         cc_1,cc_2|
|tb_order_status_1|      oms|tb_order_status|     tb_1,tb_2,tb_4,tb_5,tb_6,tb_7|
|   oms_cc_order_5|      oms|       cc_order|                              cc_5|
|   tb_order_3    |      tbc|       tb_order|                       tb_12,tb_13|
|    hwc_ord_exp_2|      hwc|   order_export|                    cc_5,tb_8,tb_9|
|   imwc_ord_exp_2|     imwc|   order_export|            cc_5,tb_10,tb_11,tb_12|
|       hwc_fakt_3|      hwc|           fakt|                    cc_5,tb_8,tb_9|
|      imwc_fakt_3|     imwc|           fakt|            cc_5,tb_10,tb_11,tb_12|
|        hwc_pod_2|      hwc|            pod|                    tb_3,tb_4,cc_3|
|       imwc_pod_2|     imwc|            pod|          cc_1,tb_4,tb_5,tb_6,tb_7|
|     hwc_return_1|      hwc|         return|                         cc_2,tb_1|
|    imwc_return_1|     imwc|         return|                    cc_1,tb_5,tb_7|
|cc_order_status_2|      oms|cc_order_status|                    cc_1,cc_2,cc_3|
|tb_order_status_2|      oms|tb_order_status|     tb_1,tb_3,tb_4,tb_5,tb_6,tb_7|
|   oms_cc_order_6|      oms|       cc_order|                              cc_6|
|       hwc_fakt_4|      hwc|           fakt|                        cc_6,tb_13|
|      imwc_fakt_4|     imwc|           fakt|                             tb_13|
When S3 object creation notifications have been triggered
Then all the orders archives are indexed:
|orderIdKey|                                                                                                                                        fileNameKeys|
|      cc_1|oms_cc_order_1,hwc_fakt_1,imwc_fakt_1,hwc_ord_exp_1,imwc_ord_exp_1,hwc_pod_1,imwc_pod_1,imwc_pod_2,imwc_return_1,cc_order_status_1,cc_order_status_2|
|      cc_2|                                                  oms_cc_order_2,hwc_fakt_1,hwc_ord_exp_1,hwc_pod_1,hwc_return_1,cc_order_status_1,cc_order_status_2|
|      cc_3|                                                                                               oms_cc_order_3,hwc_fakt_2,hwc_pod_2,cc_order_status_2|
|      cc_4|                                                                                                                          oms_cc_order_4,imwc_fakt_2|
|      cc_5|                                                                                  oms_cc_order_5,hwc_ord_exp_2,imwc_ord_exp_2,hwc_fakt_3,imwc_fakt_3|
|      cc_6|                                                                                                                           oms_cc_order_6,hwc_fakt_4|
|      tb_1|                                                                    tb_order_1,hwc_fakt_2,hwc_pod_1,hwc_return_1,tb_order_status_1,tb_order_status_2|
|      tb_2|                                                                                                   tb_order_1,hwc_fakt_2,hwc_pod_1,tb_order_status_1|
|      tb_3|                                                                     tb_order_1,hwc_fakt_2,imwc_fakt_2,hwc_pod_2,tb_order_status_2,tb_order_status_2|
|      tb_4|                                               tb_order_1,hwc_fakt_2,imwc_fakt_2,imwc_pod_1,hwc_pod_2,imwc_pod_2,tb_order_status_1,tb_order_status_2|
|      tb_5|                                                      tb_order_1,imwc_fakt_2,imwc_pod_1,imwc_pod_2,imwc_return_1,tb_order_status_1,tb_order_status_2|
|      tb_6|                                                                    tb_order_1,imwc_fakt_2,imwc_pod_1,imwc_pod_2,tb_order_status_1,tb_order_status_2|
|      tb_7|                                                      tb_order_1,imwc_fakt_2,imwc_pod_1,imwc_pod_2,imwc_return_1,tb_order_status_1,tb_order_status_2|
|      tb_8|                                                                                                                 tb_order_2,hwc_ord_exp_2,hwc_fakt_3|
|      tb_9|                                                                                                                 tb_order_2,hwc_ord_exp_2,hwc_fakt_3|
|     tb_10|                                                                                                               tb_order_2,imwc_ord_exp_2,imwc_fakt_3|
|     tb_11|                                                                                                               tb_order_2,imwc_ord_exp_2,imwc_fakt_3|
|     tb_12|                                                                                                               tb_order_3,imwc_ord_exp_2,imwc_fakt_3|
|     tb_13|                                                                                                                   tb_order_3,hwc_fakt_4,imwc_fakt_4|
When order bundle is downloaded for order cc_2
Then order bundle contains the files with the following directory structure :
|directoryInBundle                  |fileNameKeys                       |
|order_import_sfcc                  |oms_cc_order_2                     |
|order_status_import_fakt_hermes    |hwc_fakt_1                         |
|order_status_import_pod_hermes     |hwc_pod_1                          |
|order_status_import_return_hermes  |hwc_return_1                       |
|order_export_hermes                |hwc_ord_exp_1                      |
|order_status_export_sfcc           |cc_order_status_1,cc_order_status_2|
When order bundle is downloaded for order tb_7
Then order bundle contains the files with the following directory structure :
|directoryInBundle              |fileNameKeys                       |
|order_import_tb                |tb_order_1                         |
|order_status_import_fakt_im    |imwc_fakt_2                        |
|order_status_import_pod_im     |imwc_pod_1,imwc_pod_2              |
|order_status_import_return_im  |imwc_return_1                      |
|order_status_export_tb         |tb_order_status_1,tb_order_status_2|
When order bundle is downloaded for order tb_11
Then order bundle contains the files with the following directory structure :
|directoryInBundle          |fileNameKeys   |
|order_import_tb            |tb_order_2     |
|order_export_im            |imwc_ord_exp_2 |
|order_status_import_fakt_im|imwc_fakt_3    |

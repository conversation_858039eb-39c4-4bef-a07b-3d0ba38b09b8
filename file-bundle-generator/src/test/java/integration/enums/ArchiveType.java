package integration.enums;

import com.amazonaws.util.IOUtils;
import fbg.configuration.OmsConfiguration;
import fbg.configuration.TbcConfiguration;
import integration.model.InMemoryArchive;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Enum that used to map and generate the archive type.
 */
public enum ArchiveType {
    /**
     * Sfcc order archive type.
     */
    CC_ORDER("cc_order") {

        private final AtomicInteger orderIdSequence = new AtomicInteger();

        private final String template;
        {
            try {
                template = IOUtils.toString(ArchiveType.class.getResourceAsStream("/fbg/integration/template/sfcc-order-template.xml"));
            } catch (IOException e) {
                throw new RuntimeException("Can't find cc order template");
            }
        }

        @Override
        public InMemoryArchive generateArchive(List<String> orderIds) {
            InMemoryArchive inMemoryArchive = new InMemoryArchive();
            String fileName = orderIds.get(0) + "-" + nowFormatted("MM-dd-yy_HH-mm-ss") + DOT + super.getNextId(3) + ".txt";
            inMemoryArchive.setFileName(fileName);
            inMemoryArchive.setContent(template.replace(ORDER_ID_PLACEHOLDER, orderIds.get(0)));
            inMemoryArchive.setFolder(OmsConfiguration.SFCC_ORDER_FOLDER);
            return inMemoryArchive;
        }

        @Override
        public String getNextOrderNumber() {
            return ""
                    + ArchiveType.getRandomInt(8, 9)
                    + ArchiveType.getRandomInt(1, 5)
                    + nowFormatted(ORDER_ID_DATE_PATTERN)
                    + ArchiveType.applyLeadingZeroes(orderIdSequence.getAndIncrement(), 4);
        }
    },
    /**
     * Tradebyte order archive type.
     */
    TB_ORDER("tb_order") {
        private final AtomicInteger orderIdSequence = new AtomicInteger();
        private final String templateHeader = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<ORDER_LIST>\n";
        private final String templateFooter = "\n</ORDER_LIST>";

        private final String templateItem;
        {
            try {
                templateItem = IOUtils.toString(ArchiveType.class.getResourceAsStream("/fbg/integration/template/tradebyte-order-template.xml"));
            } catch (IOException e) {
                throw new RuntimeException("Can't find tb order template");
            }
        }

        @Override
        public InMemoryArchive generateArchive(List<String> orderIds) {
            InMemoryArchive inMemoryArchive = new InMemoryArchive();
            String fileName = "ORDER_" + nowFormatted("yyyyMMdd_HH") + super.getNextId(3) + ".xml";
            inMemoryArchive.setFileName(fileName);
            String content = templateHeader;
            for (String orderId : orderIds) {
                content += templateItem.replace(ORDER_ID_PLACEHOLDER, orderId.substring(2));
            }
            content += templateFooter;
            inMemoryArchive.setContent(content);
            inMemoryArchive.setFolder(TbcConfiguration.TRADEBYTE_ORDER_FOLDER);
            return inMemoryArchive;
        }

        @Override
        public String getNextOrderNumber() {
            return "TB" + nowFormatted(ORDER_ID_DATE_PATTERN) + ArchiveType.applyLeadingZeroes(orderIdSequence.getAndIncrement(), 3);
        }
    },
    /**
     * Hermes fakt archive type.
     */
    FAKT("fakt") {

        private static final String LINE_TEMPLATE =
                "${orderId}\t124602205\t2017.11.13\t12944780\t0\t1\tLI\t1234565\t0\tHose\t1\t\t\t6682065"
                        + "\t5713442923503\t1\t0\t15115847\t\tD\t0.00\t\t20000\t4005\t\t2017.11.13\t5\t\t\n";

        private static final String FOOTER_TEMPLATE =
                "FOOTER\tt_orderstat_fakt\t20171113000619\t9\t59416\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n";

        @Override
        public InMemoryArchive generateArchive(List<String> orderIds) {
            InMemoryArchive inMemoryArchive = new InMemoryArchive();
            String fileName = "t_orderstat_fakt.40.40" + getRandom(0, 99, 2) + DOT
                    + nowFormatted(HERMES_FILENAME_TS) + super.getNextId(4) + HERMES_FILENAME_EXTENSION;
            inMemoryArchive.setFileName(fileName);
            String content = getHermesContent(orderIds, LINE_TEMPLATE, FOOTER_TEMPLATE);
            inMemoryArchive.setContent(content);
            return inMemoryArchive;
        }
    },
    /**
     * Hermes pod archive type.
     */
    POD("pod") {
        private static final String LINE_TEMPLATE =
                "${orderId}\t114590532\t2017.11.22\t13055845\t30504517\t11\tLI\t1310197\t0\tLang&#228 rmeliger"
                        + "\t1\tDK_POST\t00057125420017604862\t92671715\t5713440645100\t11\t1\t13139384\t\tK\t219.95\t"
                        + "\t0\t4001\t13326848\t2017.11.23\t5\t\t00057125420017604879\t2017.11.27\n";
        private static final String FOOTER_TEMPLATE =
                "FOOTER\tt_orderstat_pod\t20101218\t3\t18646\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n";

        @Override
        public InMemoryArchive generateArchive(List<String> orderIds) {
            InMemoryArchive inMemoryArchive = new InMemoryArchive();
            String fileName = "t_orderstat_pod.40.40" + getRandom(0, 99, 2) + DOT
                    + nowFormatted(HERMES_FILENAME_TS) + super.getNextId(4) + HERMES_FILENAME_EXTENSION;
            inMemoryArchive.setFileName(fileName);
            String content = getHermesContent(orderIds, LINE_TEMPLATE, FOOTER_TEMPLATE);
            inMemoryArchive.setContent(content);
            return inMemoryArchive;
        }
    },
    /**
     * Hermes return archive type.
     */
    RETURN("return") {
        private static final String LINE_TEMPLATE = "40\t126259902\tT\t30724688\t1\t13141842\tB\t1999\t400\t0\t0\t0"
                + "\t0\t0\t0\t0\t3\t0\t0\t0\t4005\tEUR\t93649322\t${orderId}\t1\t1\tN\t20\t3\tN\n";
        private static final String FOOTER_TEMPLATE = "FOOTER\tt_returnsum\t20101218\t3\t39469\n";

        @Override
        public InMemoryArchive generateArchive(List<String> orderIds) {
            InMemoryArchive inMemoryArchive = new InMemoryArchive();
            String fileName = "t_returnsum.40." + nowFormatted(HERMES_FILENAME_TS) + super.getNextId(4) + HERMES_FILENAME_EXTENSION;
            inMemoryArchive.setFileName(fileName);
            String content = getHermesContent(orderIds, LINE_TEMPLATE, FOOTER_TEMPLATE);
            inMemoryArchive.setContent(content);
            return inMemoryArchive;
        }
    },
    /**
     * Order export archive type.
     */
    ORDER_EXPORT("order_export") {
        private final AtomicInteger orderIdSequence = new AtomicInteger();
        private final String templateHeader = "<amos_orderlist xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" "
                + "xsi:noNamespaceSchemaLocation=\"amos_orderlist_import.xsd\">\n\n";
        private final String templateFooter = "\n</amos_orderlist>";

        private final String templateItem;
        {
            try {
                templateItem = IOUtils.toString(ArchiveType.class.getResourceAsStream("/fbg/integration/template/order-export-template.xml"));
            } catch (IOException e) {
                throw new RuntimeException("Can't find order export template");
            }
        }

        @Override
        public InMemoryArchive generateArchive(List<String> orderIds) {
            InMemoryArchive inMemoryArchive = new InMemoryArchive();
            String fileName = "Order_Import_40_" + nowFormatted("yyMMddHHmmss") + super.getNextId(3) + ".xml";
            inMemoryArchive.setFileName(fileName);
            String content = templateHeader;
            for (String orderId : orderIds) {
                content += templateItem.replace(ORDER_ID_PLACEHOLDER, orderId);
            }
            content += templateFooter;
            inMemoryArchive.setContent(content);
            return inMemoryArchive;
        }
    },
    /**
     * Sfcc Order status archive type.
     */
    CC_ORDER_STATUS("cc_order_status") {
        private final AtomicInteger orderIdSequence = new AtomicInteger();
        private final String templateHeader = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>"
                + "<orders xsi:noNamespaceSchemaLocation=\"orderstatus.xsd\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\">\n\n";
        private final String templateFooter = "\n</orders>";

        private final String templateItem;
        {
            try {
                templateItem = IOUtils.toString(ArchiveType.class.getResourceAsStream("/fbg/integration/template/sfcc-order-status-template.xml"));
            } catch (IOException e) {
                throw new RuntimeException("Can't find cc order status template");
            }
        }

        @Override
        public InMemoryArchive generateArchive(List<String> orderIds) {
            InMemoryArchive inMemoryArchive = new InMemoryArchive();
            String fileName = "DMW-OrderStatus-" + nowFormatted("yyyyMMddHHmmss") + super.getNextId(3) + ".xml";
            inMemoryArchive.setFileName(fileName);
            inMemoryArchive.setFolder(OmsConfiguration.SFCC_ORDER_STATUS_FOLDER);
            String content = templateHeader;
            for (String orderId : orderIds) {
                content += templateItem.replace(ORDER_ID_PLACEHOLDER, orderId);
            }
            content += templateFooter;
            inMemoryArchive.setContent(content);
            return inMemoryArchive;
        }
    },
    /**
     * Tradebyte Order status archive type.
     */
    TB_ORDER_STATUS("tb_order_status") {
        private final AtomicInteger orderIdSequence = new AtomicInteger();
        private final String templateHeader = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>"
                + "<MESSAGES_LIST xsi:noNamespaceSchemaLocation=\"tradebyte_order_import_tb1.xsd\" "
                + "xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\">\n";
        private final String templateFooter = "\n</MESSAGES_LIST>";

        private final String templateItem;
        {
            try {
                templateItem = IOUtils.toString(ArchiveType.class.getResourceAsStream(
                        "/fbg/integration/template/tradebyte-order-status-template.xml"));
            } catch (IOException e) {
                throw new RuntimeException("Can't find tb order status template");
            }
        }

        @Override
        public InMemoryArchive generateArchive(List<String> orderIds) {
            InMemoryArchive inMemoryArchive = new InMemoryArchive();
            String fileName = "TB-OrderStatus-" + nowFormatted("yyyyMMddHHmmss") + super.getNextId(3) + ".xml";
            inMemoryArchive.setFileName(fileName);
            inMemoryArchive.setFolder(OmsConfiguration.TB_ORDER_STATUS_FOLDER);
            String content = templateHeader;
            for (String orderId : orderIds) {
                content += templateItem.replace(ORDER_ID_PLACEHOLDER, orderId.substring(2));
            }
            content += templateFooter;
            inMemoryArchive.setContent(content);
            return inMemoryArchive;
        }
    };

    private static final String ORDER_ID_DATE_PATTERN = "MMdd";
    private static final String DOT = ".";
    private static final String HERMES_FILENAME_EXTENSION = ".asc";
    private static final String HERMES_FILENAME_TS = "yyyyMMddHH";
    private static final String ORDER_ID_PLACEHOLDER = "${orderId}";

    private static Random random = new Random();
    private static final Map<String, ArchiveType> ARCHIVE_TYPE_KEY_MAP =
            Stream.of(values()).collect(Collectors
                    .toMap(ArchiveType::getArchiveTypeKey, Function.identity()));
    private String archiveTypeKey;
    private AtomicInteger sequence = new AtomicInteger();

    ArchiveType(String archiveTypeKey) {
        this.archiveTypeKey = archiveTypeKey;
    }

    /**
     * Returns the ArchiveType enum from a key.
     *
     * @param archiveTypeKey
     * @return
     */
    public static ArchiveType getArchiveType(String archiveTypeKey) {
        final ArchiveType archiveType = ARCHIVE_TYPE_KEY_MAP.get(archiveTypeKey);
        Objects.requireNonNull(archiveType, "Archive type not mapped");
        return archiveType;
    }

    public String getArchiveTypeKey() {
        return archiveTypeKey;
    }

    public String getNextOrderNumber() {
        throw new UnsupportedOperationException("Only order archive types support this method");
    }

    public abstract InMemoryArchive generateArchive(List<String> orderIds);

    private String getNextId(int leadingZeroes) {
        int i = sequence.getAndIncrement();
        return applyLeadingZeroes(i, leadingZeroes);
    }

    private static String getRandom(int from, int to, int leadingZeroes) {
        int i = random.nextInt(to - from + 1) + from;
        return applyLeadingZeroes(i, leadingZeroes);

    }

    private static int getRandomInt(int from, int to) {
        return random.nextInt(to - from + 1) + from;
    }

    private static String applyLeadingZeroes(int i, int leadingZeroes) {
        if (leadingZeroes > 0) {
            return String.format("%0" + leadingZeroes + "d", i);
        } else {
            return String.valueOf(i);
        }
    }

    private static String nowFormatted(String pattern) {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern(pattern));
    }

    private static String getHermesContent(List<String> orderIds, String lineTemplate, String footerTemplate) {
        String content = "";
        for (String orderId : orderIds) {
            int orderLines = getRandomInt(1, 10);
            for (int i = 0; i < orderLines; i++) {
                content += lineTemplate.replace(ORDER_ID_PLACEHOLDER, orderId);
            }
        }
        content += footerTemplate;
        return content;
    }

}

package integration.enums;

import fbg.configuration.HwcConfiguration;
import fbg.configuration.ImwcConfiguration;
import fbg.configuration.OmsConfiguration;
import fbg.configuration.TbcConfiguration;
import integration.model.InMemoryArchive;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * En<PERSON> used to map and get the bucket name and the folder where the archive should be put.
 */
public enum Bucket {
    /**
     * Oms s3 bucket.
     */
    OMS("oms", OmsConfiguration.getInstance().getBucketName()) {

        private final Map<ArchiveType, String> folderMap = new HashMap<>();
        {
            folderMap.put(ArchiveType.CC_ORDER, OmsConfiguration.SFCC_ORDER_FOLDER);
            folderMap.put(ArchiveType.CC_ORDER_STATUS, OmsConfiguration.SFCC_ORDER_STATUS_FOLDER);
            folderMap.put(ArchiveType.TB_ORDER_STATUS, OmsConfiguration.TB_ORDER_STATUS_FOLDER);
        }

        @Override
        String getFolder(ArchiveType archiveType) {
            return folderMap.get(archiveType);
        }
    },
    /**
     * Tbc s3 bucket.
     */
    TBC("tbc", TbcConfiguration.getInstance().getBucketName()) {

        private final Map<ArchiveType, String> folderMap = new HashMap<>();
        {
            folderMap.put(ArchiveType.TB_ORDER, TbcConfiguration.TRADEBYTE_ORDER_FOLDER);

        }

        @Override
        String getFolder(ArchiveType archiveType) {
            return folderMap.get(archiveType);
        }
    },
    /**
     * Hwc s3 bucket.
     */
    HWC("hwc", HwcConfiguration.getInstance().getBucketName()) {
        private final Map<ArchiveType, String> folderMap = new HashMap<>();
        {
            folderMap.put(ArchiveType.FAKT, HwcConfiguration.FAKT_FOLDER);
            folderMap.put(ArchiveType.POD, HwcConfiguration.POD_FOLDER);
            folderMap.put(ArchiveType.RETURN, HwcConfiguration.RETURNS_FOLDER);
            folderMap.put(ArchiveType.ORDER_EXPORT, HwcConfiguration.ORDER_EXPORT_FOLDER);
        }

        @Override
        String getFolder(ArchiveType archiveType) {
            return folderMap.get(archiveType);
        }
    },
    /**
     * Imwc s3 bucket.
     */
    IMWC("imwc", ImwcConfiguration.getInstance().getBucketName()) {
        @Override
        String getFolder(ArchiveType archiveType) {
            return null;
        }
    };

    private static final Map<String, Bucket> BUCKET_KEY_MAP =
            Stream.of(values()).collect(Collectors
                    .toMap(Bucket::getBucketKey, Function.identity()));
    private String bucketKey;
    private String bucketName;

    Bucket(String bucketKey, String bucketName) {
        this.bucketKey = bucketKey;
        this.bucketName = bucketName;
    }

    private String getBucketName() {
        return bucketName;
    }

    private String getBucketKey() {
        return bucketKey;
    }

    public void enrich(InMemoryArchive inMemoryArchive, ArchiveType archiveType) {
        inMemoryArchive.setBucket(bucketName);
        inMemoryArchive.setFolder(getFolder(archiveType));
    }

    abstract String getFolder(ArchiveType archiveType);

    /**
     * Get the mapped Bucket enum from the bucketKey.
     *
     * @param bucketKey
     * @return
     */
    public static Bucket getBucket(String bucketKey) {
        Bucket bucket = BUCKET_KEY_MAP.get(bucketKey);
        Objects.requireNonNull(bucket, "Couldn't find a bucket for " + bucketKey);
        return bucket;
    }
}

package integration.model;

/**
 * Model that contains the content and the information of an archive used for test.
 */
public class InMemoryArchive {

    private String bucket;

    private String folder;

    private String fileName;

    private String content;

    public String makeObjectKey() {
        return getFolder() == null || getFolder().isEmpty() ? getFileName() : String.join("/", getFolder(), getFileName());
    }

    public String getBucket() {
        return bucket;
    }

    public void setBucket(String bucket) {
        this.bucket = bucket;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFolder() {
        return folder;
    }

    public void setFolder(String folder) {
        this.folder = folder;
    }
}

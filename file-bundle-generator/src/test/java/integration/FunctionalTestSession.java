package integration;

import com.amazonaws.services.s3.AmazonS3;
import fbg.context.BeanFactory;
import fbg.service.ElasticSearchService;
import integration.service.ArchiveGeneratorService;

/**
 * Dependency management for functional tests.
 */
public final class FunctionalTestSession {
    private static FunctionalTestSession instance = new FunctionalTestSession();

    private ArchiveGeneratorService archiveGeneratorService = new ArchiveGeneratorService();
    private ElasticSearchService elasticSearchService = BeanFactory.getInstance().getElasticSearchService();
    private AmazonS3 amazonS3 = BeanFactory.getInstance().getAmazonS3();

    private FunctionalTestSession() {
    }

    public static FunctionalTestSession getInstance() {
        return instance;
    }

    public ArchiveGeneratorService getArchiveGeneratorService() {
        return archiveGeneratorService;
    }

    public ElasticSearchService getElasticSearchService() {
        return elasticSearchService;
    }

    public AmazonS3 getAmazonS3() {
        return amazonS3;
    }
}

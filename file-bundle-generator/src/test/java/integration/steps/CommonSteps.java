package integration.steps;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import fbg.configuration.ElasticSearchConfiguration;
import fbg.configuration.HwcConfiguration;
import fbg.configuration.ImwcConfiguration;
import fbg.configuration.OmsConfiguration;
import fbg.configuration.OsimcConfiguration;
import fbg.configuration.S3Configuration;
import fbg.configuration.TbcConfiguration;
import fbg.context.BeanFactory;
import org.jbehave.core.annotations.Given;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * Step that setup the environment for functional test.
 */
public class CommonSteps {
    /**
     * Configuration step.
     */
    @Given("configuration is set up")
    public void setupConfiguration() {
        ReflectionTestUtils.setField(HwcConfiguration.getInstance(), "bucketName", "bse-hwc-archive-dev.bseint.io");
        ReflectionTestUtils.setField(OmsConfiguration.getInstance(), "bucketName", "bse-oms-archive-dev.bseint.io");
        ReflectionTestUtils.setField(ImwcConfiguration.getInstance(), "bucketName", "bse-imwc-archive-dev.bseint.io");
        ReflectionTestUtils.setField(ElasticSearchConfiguration.getInstance(), "endpoint", "http://localhost:9200");
        ReflectionTestUtils.setField(S3Configuration.getInstance(), "bucketName", "bse-fbg-archive-dev.bseint.io");
        ReflectionTestUtils.setField(TbcConfiguration.getInstance(), "bucketName", "bse-tbc-archive-dev.bseint.io");
        ReflectionTestUtils.setField(OsimcConfiguration.getInstance(), "bucketName", "bse-osimc-archive-dev.bseint.io");
        BasicAWSCredentials awsCredentials = new BasicAWSCredentials("foobar", "foobar");
        AmazonS3 amazonS3 = AmazonS3ClientBuilder.standard().withEndpointConfiguration(
                new AwsClientBuilder.EndpointConfiguration("http://localhost:4572", "eu-west-1"))
                .withCredentials(new AWSStaticCredentialsProvider(awsCredentials))
                .withPathStyleAccessEnabled(true)
                .build();
        BeanFactory beanFactory = BeanFactory.getInstance();
        ReflectionTestUtils.setField(beanFactory, "amazonS3", amazonS3);
    }
}

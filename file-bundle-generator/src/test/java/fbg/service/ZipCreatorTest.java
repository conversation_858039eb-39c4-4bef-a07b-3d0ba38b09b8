package fbg.service;

import org.apache.commons.io.IOUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

import static org.junit.Assert.*;

/**
 * Unit tests for {@link ZipCreator}.
 */
public class ZipCreatorTest {
    private ZipCreator zipCreator;
    private ByteArrayOutputStream outputStream;
    private InputStream inputStream;

    @Before
    public void setUp() {
        zipCreator = new ZipCreator();
        outputStream = new ByteArrayOutputStream();
        inputStream = IOUtils.toInputStream("dummy input", StandardCharsets.UTF_8);
    }

    /**
     * Closes the streams.
     */
    @After
    public void tearDown() throws IOException {
        if (outputStream != null) {
            outputStream.close();
        }

        if (inputStream != null) {
            inputStream.close();
        }
    }

    @Test
    public void addToArchive_contentGiven_contentAddedToArchive() throws IOException {
        // act
        try (ZipOutputStream zipOutputStream = new ZipOutputStream(outputStream)) {
            zipCreator.addToArchive(zipOutputStream, inputStream, "dummyPath");
        }

        // assert
        try (ZipInputStream zipInputStream = new ZipInputStream(new ByteArrayInputStream(outputStream.toByteArray()))) {
            ZipEntry zipEntry = zipInputStream.getNextEntry();
            assertEquals("File content mismatch", "dummy input", IOUtils.toString(zipInputStream, StandardCharsets.UTF_8));
            assertEquals("File path mismatch", "dummyPath", zipEntry.getName());
        }
    }
}

package fbg.service;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import fbg.configuration.ElasticSearchConfiguration;
import fbg.generated.elasticsearch.model.ArchiveContent;
import fbg.generated.elasticsearch.model.CountsApiResponse;
import fbg.generated.elasticsearch.model.Hit;
import fbg.generated.elasticsearch.model.Hits;
import fbg.generated.elasticsearch.model.SearchArchiveContentResponse;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpStatus;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import java.lang.reflect.Field;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.ArgumentMatchers.any;

/**
 * Unit test for {@link ElasticSearchService}.
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class ElasticSearchServiceTest {

    private static final String TEST_LOCAL_URI = "https://lambda.com/files/file/";
    private static final String ORDER_ID = "orderId";
    private static Logger logger;
    private static MockedStatic<HttpClients> httpClientsMock;
    private static MockedStatic<LoggerFactory> loggerFactoryMock;

    private ArchiveContent archiveContent;
    private StatusLine statusLine;
    private CloseableHttpClient httpClient;
    private ElasticSearchService elasticSearchService;
    private CloseableHttpResponse response;

    @BeforeAll
    public static void setUpClass() {
        logger = Mockito.mock(Logger.class);
        loggerFactoryMock = mockStatic(LoggerFactory.class);
        loggerFactoryMock.when(() -> LoggerFactory.getLogger(Mockito.any(Class.class))).thenReturn(logger);

        httpClientsMock = mockStatic(HttpClients.class);
    }

    @AfterAll
    public static void tearDownClass() {
        loggerFactoryMock.close();
        httpClientsMock.close();
    }

    @BeforeEach
    public void setUp() throws IOException {
        ReflectionTestUtils.setField(ElasticSearchConfiguration.getInstance(), "endpoint", "https://lambda.com");
        httpClient = mock(CloseableHttpClient.class);
        httpClientsMock.when(HttpClients::createDefault).thenReturn(httpClient);

        archiveContent = new ArchiveContent();
        archiveContent.setS3bucket("S3_BUCKET");
        archiveContent.setType("TYPE");
        archiveContent.setFilePath("FILENAME");
        response = mock(CloseableHttpResponse.class);
        statusLine = mock(StatusLine.class);
        when(response.getStatusLine()).thenReturn(statusLine);
        when(httpClient.execute(any(HttpRequestBase.class))).thenReturn(response);

        elasticSearchService = new ElasticSearchService();
    }

    @Test
    public void putDocument_archiveContentCreated_returnTrue() throws Exception {
        // arrange
        when(statusLine.getStatusCode()).thenReturn(HttpStatus.SC_CREATED);

        // act
        boolean result = elasticSearchService.putOrderDocument(archiveContent);

        // assert
        assertTrue(result, "ArchiveContentCreated: Document should be uploaded successfully");
        ArgumentCaptor<HttpPost> captor = ArgumentCaptor.forClass(HttpPost.class);
        verify(httpClient).execute(captor.capture());
        assertEquals(TEST_LOCAL_URI, captor.getValue().getURI().toString(),
                "ArchiveContentCreated: Constructed URI and the test URI are mismatch");
        assertEquals(ContentType.APPLICATION_JSON.getMimeType(), (captor.getValue().getHeaders(HttpHeaders.CONTENT_TYPE))[0].getValue(),
                "ArchiveContentCreated: Request Content-type header is not application/json ");
    }

    @Test
    public void putDocument_archiveContentUpdated_returnTrue() throws Exception {
        // arrange
        when(statusLine.getStatusCode()).thenReturn(HttpStatus.SC_OK);

        // act
        boolean result = elasticSearchService.putOrderDocument(archiveContent);

        // assert
        assertTrue(result, "ArchiveContentUpdated: Document should be uploaded successfully");
        ArgumentCaptor<HttpPost> captor = ArgumentCaptor.forClass(HttpPost.class);
        verify(httpClient).execute(captor.capture());
        assertEquals(TEST_LOCAL_URI, captor.getValue().getURI().toString(),
                "ArchiveContentUpdated: Constructed URI and the test URI are mismatch");
        assertEquals(ContentType.APPLICATION_JSON.getMimeType(), (captor.getValue().getHeaders(HttpHeaders.CONTENT_TYPE))[0].getValue(),
                "ArchiveContentUpdated: Request Content-type header is not application/json ");
    }

    @Test
    public void putDocument_archiveContentFailed_returnFalse() throws IOException {
        // arrange
        when(statusLine.getStatusCode()).thenReturn(HttpStatus.SC_BAD_REQUEST);

        // act
        boolean result = elasticSearchService.putOrderDocument(archiveContent);

        // assert
        assertFalse(result, "ArchiveContentFailed: Document shouldn't be uploaded successfully");
        ArgumentCaptor<HttpPost> captor = ArgumentCaptor.forClass(HttpPost.class);
        verify(httpClient).execute(captor.capture());
        assertEquals(TEST_LOCAL_URI, captor.getValue().getURI().toString(),
                "ArchiveContentFailed: Constructed URI and the test URI are mismatch");
        assertEquals(ContentType.APPLICATION_JSON.getMimeType(), (captor.getValue().getHeaders(HttpHeaders.CONTENT_TYPE))[0].getValue(),
                "ArchiveContentFailed: Request Content-type header is not application/json ");
    }

    @Test
    public void putDocument_archiveContentFailedDueToIOException_elasticSearchExceptionThrown() throws IOException {
        // arrange
        when(httpClient.execute(any())).thenThrow(new IOException());

        // act & assert
        assertThrows(ElasticSearchException.class, () -> {
            elasticSearchService.putOrderDocument(archiveContent);
        });
    }

    @Test
    public void getOrderDocuments_documentsRequested_documentsReceived() throws IOException {
        // arrange
        ObjectMapper objectMapper = new ObjectMapper()
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .registerModule(new JavaTimeModule());
        HttpEntity httpEntity = mock(HttpEntity.class);

        CountsApiResponse countsApiResponse =
                new CountsApiResponse().withCount(1);

        SearchArchiveContentResponse contentResponse =
                new SearchArchiveContentResponse()
                        .withHits(new Hits().withHits(Collections.singletonList(new Hit().withSource(archiveContent)))
                                .withTotal(1));

        when(httpEntity.getContent())
                .thenReturn(IOUtils.toInputStream(objectMapper.writeValueAsString(countsApiResponse), StandardCharsets.UTF_8))
                .thenReturn(IOUtils.toInputStream(objectMapper.writeValueAsString(contentResponse), StandardCharsets.UTF_8));
        when(response.getEntity()).thenReturn(httpEntity);
        when(statusLine.getStatusCode()).thenReturn(HttpStatus.SC_OK);

        // act
        List<ArchiveContent> actualArchiveContents = elasticSearchService.getOrderDocuments(ORDER_ID);

        // assert
        assertEquals(archiveContent.getS3bucket(), actualArchiveContents.get(0).getS3bucket(), "Bucket name mismatch");
        assertEquals(archiveContent.getFilePath(), actualArchiveContents.get(0).getFilePath(), "File path mismatch");
        assertEquals(archiveContent.getOrders(), actualArchiveContents.get(0).getOrders(), "Order list mismatch");

    }

    @Test
    public void getOrderDocuments_documentNotFound_emptyListReceived() throws IOException {
        // arrange
        ObjectMapper objectMapper = new ObjectMapper()
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .registerModule(new JavaTimeModule());
        HttpEntity httpEntity = mock(HttpEntity.class);
        CountsApiResponse countsApiResponse =
                new CountsApiResponse().withCount(1);
        when(response.getEntity()).thenReturn(httpEntity);
        when(httpEntity.getContent())
                .thenReturn(IOUtils.toInputStream(objectMapper.writeValueAsString(countsApiResponse), StandardCharsets.UTF_8));

        when(statusLine.getStatusCode()).thenReturn(HttpStatus.SC_OK).thenReturn(HttpStatus.SC_NOT_FOUND);

        // act
        List<ArchiveContent> actualArchiveContents = elasticSearchService.getOrderDocuments(ORDER_ID);

        // assert
        assertTrue(actualArchiveContents.isEmpty());
    }

    @Test
    public void getOrderDocuments_handlingException_elasticSearchExceptionThrown() throws IOException {
        // arrange
        when(httpClient.execute(any(HttpRequestBase.class))).thenThrow(IOException.class);

        // act & assert
        assertThrows(ElasticSearchException.class, () -> {
            elasticSearchService.getOrderDocuments(ORDER_ID);
        });
    }

    @Test
    public void getOrderDocuments_countsFails_elasticSearchExceptionThrown() {
        // arrange
        when(response.getStatusLine().getStatusCode()).thenReturn(HttpStatus.SC_NOT_FOUND);
        // act
        try {
            elasticSearchService.getOrderDocuments(ORDER_ID);
        } catch (ElasticSearchException ex) {
            assertTrue(ex.getMessage().contains(((Integer) HttpStatus.SC_NOT_FOUND).toString()));
        }
    }

}

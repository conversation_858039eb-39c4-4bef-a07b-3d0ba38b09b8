package fbg.service;

import com.amazonaws.HttpMethod;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.PutObjectRequest;
import fbg.context.BeanFactory;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.util.Date;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link S3Service}.
 */
@ExtendWith(MockitoExtension.class)
public class S3ServiceTest {
    private static final String OBJECT_KEY = "OBJECT_KEY";
    private static final String BUCKET_NAME = "BUCKET_NAME";
    private static Logger logger;
    private static BeanFactory beanFactory;
    private static MockedStatic<LoggerFactory> loggerFactoryMock;
    private static MockedStatic<BeanFactory> beanFactoryMock;

    private AmazonS3 amazonS3;
    private S3Service s3Service;

    /**
     * Setup for static mocks.
     */
    @BeforeAll
    public static void setUpClass() {
        logger = Mockito.mock(Logger.class);
        loggerFactoryMock = mockStatic(LoggerFactory.class);
        loggerFactoryMock.when(() -> LoggerFactory.getLogger(Mockito.any(Class.class))).thenReturn(logger);

        beanFactory = Mockito.mock(BeanFactory.class);
        beanFactoryMock = mockStatic(BeanFactory.class);
        beanFactoryMock.when(BeanFactory::getInstance).thenReturn(beanFactory);
    }

    @AfterAll
    public static void tearDownClass() {
        loggerFactoryMock.close();
        beanFactoryMock.close();
    }

    @BeforeEach
    public void setUp() {
        amazonS3 = mock(AmazonS3.class);
        when(beanFactory.getAmazonS3()).thenReturn(amazonS3);
        s3Service = new S3Service();
    }

    @Test
    public void getS3Object_s3EventNotificationRecord_callAmazonS3() {
        // act
        s3Service.getObjectFromBucket(BUCKET_NAME, OBJECT_KEY);

        // assert
        ArgumentCaptor<GetObjectRequest> captor = ArgumentCaptor.forClass(GetObjectRequest.class);
        verify(amazonS3).getObject(captor.capture());
        assertEquals("Object key mismatch", OBJECT_KEY, captor.getValue().getKey());
        assertEquals("Bucket name mismatch", BUCKET_NAME, captor.getValue().getBucketName());
    }

    @Test
    public void putFileToBucket_uploadRequested_objectUploaded() {
        // arrange
        File mockFile = mock(File.class);

        // act
        s3Service.putFileToBucket(BUCKET_NAME, OBJECT_KEY, mockFile);

        // assert
        ArgumentCaptor<PutObjectRequest> argumentCaptor = ArgumentCaptor.forClass(PutObjectRequest.class);
        verify(amazonS3).putObject(argumentCaptor.capture());
        assertEquals("Object key mismatch", OBJECT_KEY, argumentCaptor.getValue().getKey());
        assertEquals("Bucket name mismatch", BUCKET_NAME, argumentCaptor.getValue().getBucketName());
    }

    @Test
    public void generateDownloadUrl_urlRequested_urlGenerated() {
        // act
        s3Service.generateDownloadUrl(BUCKET_NAME, OBJECT_KEY);

        // assert
        verify(amazonS3).generatePresignedUrl(eq(BUCKET_NAME), eq(OBJECT_KEY), any(Date.class), eq(HttpMethod.GET));
    }
}

package fbg.functions;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.events.S3Event;
import com.amazonaws.services.lambda.runtime.events.models.s3.S3EventNotification;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import fbg.context.BeanFactory;
import fbg.generated.elasticsearch.model.ArchiveContent;
import fbg.service.ArchiveProcessService;
import fbg.service.ElasticSearchService;
import fbg.service.S3Service;
import org.slf4j.Logger;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Test class for {@link S3EventHandler}.
 */
@ExtendWith(MockitoExtension.class)
public class S3EventHandlerTest {

    private static final String TYPE = "Type";
    private static final List<String> ORDERS = Arrays.asList("ORDER_1", "ORDER_2");
    private static final String BUCKET_NAME = "BucketName";
    private static final String KEY = "key";

    @Mock
    private S3Service s3Service;

    @Mock
    private ArchiveProcessService archiveProcessService;

    @Mock
    private ElasticSearchService elasticSearchService;

    @InjectMocks
    private S3EventHandler s3EventHandler;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void handleRequest_s3EventCorrect_documentSaved() {
        // Arrange
        S3Event s3Event = mock(S3Event.class);
        Context context = mock(Context.class);

        S3EventNotification.S3EventNotificationRecord record = mock(S3EventNotification.S3EventNotificationRecord.class);
        S3EventNotification.S3Entity s3Entity = mock(S3EventNotification.S3Entity.class);
        S3EventNotification.S3BucketEntity bucketEntity = mock(S3EventNotification.S3BucketEntity.class);
        S3EventNotification.S3ObjectEntity objectEntity = mock(S3EventNotification.S3ObjectEntity.class);

        when(record.getS3()).thenReturn(s3Entity);
        when(s3Entity.getBucket()).thenReturn(bucketEntity);
        when(s3Entity.getObject()).thenReturn(objectEntity);
        when(bucketEntity.getName()).thenReturn("bucketName");
        when(objectEntity.getKey()).thenReturn("objectKey");

        List<S3EventNotification.S3EventNotificationRecord> records = Collections.singletonList(record);
        when(s3Event.getRecords()).thenReturn(records);

        S3Object s3Object = mock(S3Object.class);
        when(s3Service.getObjectFromBucket("bucketName", "objectKey")).thenReturn(s3Object);

        ArchiveContent archiveContent = new ArchiveContent();
        archiveContent.setType("someType");
        archiveContent.setOrders(Collections.emptyList());
        when(archiveProcessService.processS3Object(s3Object)).thenReturn(archiveContent);

        // Act
        String result = s3EventHandler.handleRequest(s3Event, context);

        // Assert
        assertEquals("OK", result);
        verify(s3Service).getObjectFromBucket("bucketName", "objectKey");
        verify(archiveProcessService).processS3Object(s3Object);
        verify(elasticSearchService).putOrderDocument(archiveContent);
    }

    @Test
    public void handleRequest_eventIsNull_nullPointerException() {
        // arrange
        S3Event s3event = null;

        // act & assert
        assertThrows(NullPointerException.class, () -> {
            s3EventHandler.handleRequest(s3event, null);
        });
    }

    @Test
    public void handleRequest_notificationRecordsListIsNull_nullPointerException() {
        // Arrange
        S3Event s3event = null;
        Context context = mock(Context.class);

        // Act & Assert
        NullPointerException exception = assertThrows(NullPointerException.class, () -> {
            s3EventHandler.handleRequest(s3event, context);
        });

        // Verify the exception message
        assertEquals("S3Event is null", exception.getMessage());
    }

    @Test
    public void handleRequest_notificationRecordIsNull_recordSkipped() {
        // arrange
        ArrayList<S3EventNotification.S3EventNotificationRecord> notificationRecords = new ArrayList<>();
        notificationRecords.add(null);
        S3Event s3event = new S3Event(notificationRecords);

        // act
        String result = s3EventHandler.handleRequest(s3event, null);

        // assert
        assertTrue("OK".equals(result), "NotificationRecordIsNull: Response should be 'OK'");
        verify(s3Service, never()).getObjectFromBucket(anyString(), anyString());
        verify(archiveProcessService, never()).processS3Object(any(S3Object.class));
        verify(elasticSearchService, never()).putOrderDocument(any(ArchiveContent.class));
    }

    @Test
    public void handleRequest_s3IsNull_recordSkipped() {
        // arrange
        S3Event s3Event = mock(S3Event.class);

        // act
        String result = s3EventHandler.handleRequest(s3Event, null);

        // assert
        assertTrue("OK".equals(result), "S3IsNull: Response should be 'OK'");
        verify(s3Service, never()).getObjectFromBucket(anyString(), anyString());
        verify(archiveProcessService, never()).processS3Object(any(S3Object.class));
        verify(elasticSearchService, never()).putOrderDocument(any(ArchiveContent.class));
    }

    @Test
    public void handleRequest_archiveContentTypeIsNull_recordSkipped() {
        // arrange
        S3Event s3Event = mock(S3Event.class);
        Context context = mock(Context.class);

        S3EventNotification.S3EventNotificationRecord record = mock(S3EventNotification.S3EventNotificationRecord.class);
        S3EventNotification.S3Entity s3Entity = mock(S3EventNotification.S3Entity.class);
        S3EventNotification.S3BucketEntity bucketEntity = mock(S3EventNotification.S3BucketEntity.class);
        S3EventNotification.S3ObjectEntity objectEntity = mock(S3EventNotification.S3ObjectEntity.class);

        when(record.getS3()).thenReturn(s3Entity);
        when(s3Entity.getBucket()).thenReturn(bucketEntity);
        when(s3Entity.getObject()).thenReturn(objectEntity);
        when(bucketEntity.getName()).thenReturn("bucketName");
        when(objectEntity.getKey()).thenReturn("objectKey");

        List<S3EventNotification.S3EventNotificationRecord> records = Collections.singletonList(record);
        when(s3Event.getRecords()).thenReturn(records);

        S3Object s3Object = mock(S3Object.class);
        when(s3Service.getObjectFromBucket("bucketName", "objectKey")).thenReturn(s3Object);

        ArchiveContent archiveContent = new ArchiveContent();
        archiveContent.setType(null);
        archiveContent.setOrders(null);
        when(archiveProcessService.processS3Object(s3Object)).thenReturn(archiveContent);

        // act
        String result = s3EventHandler.handleRequest(s3Event, null);

        // assert
        assertTrue("OK".equals(result), "ArchiveContentTypeIsNull: Response should be 'OK'");
        ArgumentCaptor<S3Object> s3ObjectArgumentCaptor = ArgumentCaptor.forClass(S3Object.class);
        verify(archiveProcessService).processS3Object(s3ObjectArgumentCaptor.capture());
        ArgumentCaptor<ArchiveContent> archiveContentArgumentCaptor = ArgumentCaptor.forClass(ArchiveContent.class);
        verify(elasticSearchService, never()).putOrderDocument(archiveContentArgumentCaptor.capture());
    }

    @Test
    public void handleRequest_archiveContentOrdersIsNull_recordSkipped() {
        // arrange
        S3Event s3Event = mock(S3Event.class);
        Context context = mock(Context.class);

        S3EventNotification.S3EventNotificationRecord record = mock(S3EventNotification.S3EventNotificationRecord.class);
        S3EventNotification.S3Entity s3Entity = mock(S3EventNotification.S3Entity.class);
        S3EventNotification.S3BucketEntity bucketEntity = mock(S3EventNotification.S3BucketEntity.class);
        S3EventNotification.S3ObjectEntity objectEntity = mock(S3EventNotification.S3ObjectEntity.class);

        when(record.getS3()).thenReturn(s3Entity);
        when(s3Entity.getBucket()).thenReturn(bucketEntity);
        when(s3Entity.getObject()).thenReturn(objectEntity);
        when(bucketEntity.getName()).thenReturn("bucketName");
        when(objectEntity.getKey()).thenReturn("objectKey");

        List<S3EventNotification.S3EventNotificationRecord> records = Collections.singletonList(record);
        when(s3Event.getRecords()).thenReturn(records);

        S3Object s3Object = mock(S3Object.class);
        when(s3Service.getObjectFromBucket("bucketName", "objectKey")).thenReturn(s3Object);

        ArchiveContent archiveContent = new ArchiveContent();
        archiveContent.setType("someType");
        archiveContent.setOrders(null);
        when(archiveProcessService.processS3Object(s3Object)).thenReturn(archiveContent);

        // act
        String result = s3EventHandler.handleRequest(s3Event, null);

        // assert
        assertTrue("OK".equals(result), "ArchiveContentOrdersIsNull: Response should be 'OK'");
        ArgumentCaptor<S3Object> s3ObjectArgumentCaptor = ArgumentCaptor.forClass(S3Object.class);
        verify(archiveProcessService).processS3Object(s3ObjectArgumentCaptor.capture());
        ArgumentCaptor<ArchiveContent> archiveContentArgumentCaptor = ArgumentCaptor.forClass(ArchiveContent.class);
        verify(elasticSearchService, never()).putOrderDocument(archiveContentArgumentCaptor.capture());
    }

    private S3Event createS3event(PutObjectRequest putObjectRequest) {
        List<S3EventNotification.S3EventNotificationRecord> records = new ArrayList<>();
        S3EventNotification.S3BucketEntity bucket = new S3EventNotification.S3BucketEntity(putObjectRequest.getBucketName(), null, null);
        S3EventNotification.S3ObjectEntity object = new S3EventNotification.S3ObjectEntity(putObjectRequest.getKey(), null, null, null, null);
        S3EventNotification.S3Entity s3 = new S3EventNotification.S3Entity(null, bucket, object, null);
        S3EventNotification.S3EventNotificationRecord s3EventNotificationRecord =
                new S3EventNotification.S3EventNotificationRecord(null, null, null, null, null, null, null, s3, null);
        records.add(s3EventNotificationRecord);
        S3Event s3Event = new S3Event(records);
        return s3Event;
    }
}

package fbg.functions;

import com.amazonaws.AmazonServiceException;
import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyResponseEvent;
import com.amazonaws.services.s3.model.S3Object;
import fbg.configuration.FileType;
import fbg.configuration.HwcConfiguration;
import fbg.configuration.S3Configuration;
import fbg.context.BeanFactory;
import fbg.generated.elasticsearch.model.ArchiveContent;
import fbg.service.ElasticSearchService;
import fbg.service.S3Service;
import fbg.service.ZipCreator;
import org.apache.http.HttpStatus;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Unit tests for {@link OrderBundleGenerationHandler}.
 */
@ExtendWith(MockitoExtension.class)
public class OrderBundleGenerationHandlerTest {

    private static Logger logger;
    private static BeanFactory beanFactory;
    private static MockedStatic<BeanFactory> beanFactoryMock;
    private static MockedStatic<LoggerFactory> loggerFactoryMock;
    private static MockedStatic<S3Configuration> s3ConfigMock;
    private static MockedStatic<HwcConfiguration> hwcConfigMock;
    private S3Service s3Service;
    private ElasticSearchService elasticSearchService;
    private ZipCreator zipCreator;

    private BundleGenerationHandler bundleGenerationHandler;

    /**
     * Sets up static mocks.
     */
    @BeforeAll
    public static void setUpClass() {
        logger = Mockito.mock(Logger.class);
        loggerFactoryMock = mockStatic(LoggerFactory.class);
        loggerFactoryMock.when(() -> LoggerFactory.getLogger(Mockito.any(Class.class))).thenReturn(logger);

        beanFactory = Mockito.mock(BeanFactory.class);
        beanFactoryMock = mockStatic(BeanFactory.class);
        beanFactoryMock.when(BeanFactory::getInstance).thenReturn(beanFactory);

        S3Configuration s3Configuration = mock(S3Configuration.class);
        when(s3Configuration.getBucketName()).thenReturn("bucket");
        s3ConfigMock = mockStatic(S3Configuration.class);
        s3ConfigMock.when(S3Configuration::getInstance).thenReturn(s3Configuration);

        HwcConfiguration hwcConfiguration = mock(HwcConfiguration.class);
        when(hwcConfiguration.getBucketName()).thenReturn("hwcBucket");
        hwcConfigMock = mockStatic(HwcConfiguration.class);
        hwcConfigMock.when(HwcConfiguration::getInstance).thenReturn(hwcConfiguration);
    }

    @AfterAll
    public static void tearDownClass() {
        loggerFactoryMock.close();
        beanFactoryMock.close();
        s3ConfigMock.close();
        hwcConfigMock.close();
    }

    @BeforeEach
    public void setUp() {
        this.s3Service = mock(S3Service.class);
        this.elasticSearchService = mock(ElasticSearchService.class);
        this.zipCreator = mock(ZipCreator.class);
        when(beanFactory.getS3Service()).thenReturn(this.s3Service);
        when(beanFactory.getElasticSearchService()).thenReturn(this.elasticSearchService);
        when(beanFactory.getZipCreator()).thenReturn(this.zipCreator);

        bundleGenerationHandler = new BundleGenerationHandler();
    }

    @Test
    public void handleRequest_bundleRequested_bundleGenerated() throws IOException {
        // arrange
        String orderId = "myOrder";
        String downloadUri = "http://localhost";
        APIGatewayProxyRequestEvent requestEvent = new APIGatewayProxyRequestEvent();
        requestEvent.setPathParameters(Collections.singletonMap("orderBundle", orderId));
        requestEvent.setPath("/bundle/");

        S3Object s3Object = mock(S3Object.class);
        ArchiveContent archiveContent = new ArchiveContent();
        archiveContent.setType(FileType.FAKT.name());
        archiveContent.setFilePath("fakt/fakt_file.asc");
        archiveContent.setS3bucket(HwcConfiguration.getInstance().getBucketName());
        List<ArchiveContent> archiveContents = new ArrayList<>();
        archiveContents.add(archiveContent);
        // added twice in order to test that duplicates paths are filtered and zipCreator will be called once
        archiveContents.add(archiveContent);
        when(this.elasticSearchService.getOrderDocuments(orderId)).thenReturn(archiveContents);
        when(this.s3Service.getObjectFromBucket(anyString(), anyString())).thenReturn(s3Object);
        when(this.s3Service.generateDownloadUrl(anyString(), anyString())).thenReturn(new URL(downloadUri));

        Context context = null;
        // act
        APIGatewayProxyResponseEvent responseEvent = bundleGenerationHandler.handleRequest(requestEvent, context);

        // assert
        assertEquals(HttpStatus.SC_OK, responseEvent.getStatusCode().intValue(), "HTTP status mismatch");
        assertEquals(downloadUri, responseEvent.getBody(), "HTTP body mismatch");
        verify(this.s3Service).putFileToBucket(anyString(), anyString(), any(File.class));
        verify(this.s3Service).generateDownloadUrl(anyString(), anyString());
        verify(this.zipCreator).addToArchive(any(), any(), eq("order_status_import_fakt_hermes/fakt_file.asc"));
    }

    @Test
    public void handleRequest_s3ExceptionHappens_http500Returned() {
        // arrange
        String orderId = "myOrder";
        APIGatewayProxyRequestEvent requestEvent = new APIGatewayProxyRequestEvent();
        requestEvent.setPathParameters(Collections.singletonMap("orderBundle", orderId));
        requestEvent.setPath("/bundle/");

        when(this.elasticSearchService.getOrderDocuments(orderId)).thenReturn(Collections.singletonList(new ArchiveContent()));
        when(this.s3Service.getObjectFromBucket(anyString(), anyString())).thenThrow(new AmazonServiceException(""));

        Context context = null;
        // act
        APIGatewayProxyResponseEvent responseEvent = bundleGenerationHandler.handleRequest(requestEvent, context);

        // assert
        assertEquals(HttpStatus.SC_INTERNAL_SERVER_ERROR, responseEvent.getStatusCode().intValue());
    }
}

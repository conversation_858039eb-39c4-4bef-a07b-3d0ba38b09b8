package fbg.functions;

import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyResponseEvent;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.ListObjectsV2Result;
import com.amazonaws.services.s3.model.ObjectListing;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.amazonaws.util.IOUtils;
import fbg.configuration.S3OrangeFileConfig;
import fbg.context.BeanFactory;
import fbg.service.ZipCreator;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpStatus;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * Unit tests for {@link OrangeFileGenerationHandlerTest}.
 */
@ExtendWith(MockitoExtension.class)
public class OrangeFileGenerationHandlerTest {
    private static final String ORDER_ID = "myOrder";
    private static Logger logger;
    private static BeanFactory beanFactory;
    private static MockedStatic<BeanFactory> beanFactoryMock;
    private static MockedStatic<LoggerFactory> loggerFactoryMock;
    private static MockedStatic<S3OrangeFileConfig> s3OrangeFileConfigMock;
    private static MockedStatic<IOUtils> ioUtilsMock;

    private ZipCreator zipCreator;
    private AmazonS3 amazonS3Client;
    private BundleGenerationHandler bundleGenerationHandler;

    /**
     * Sets up static mocks.
     */
    @BeforeAll
    public static void setUpClass() {
        logger = Mockito.mock(Logger.class);
        loggerFactoryMock = mockStatic(LoggerFactory.class);
        loggerFactoryMock.when(() -> LoggerFactory.getLogger(Mockito.any(Class.class))).thenReturn(logger);

        beanFactory = Mockito.mock(BeanFactory.class);
        beanFactoryMock = mockStatic(BeanFactory.class);
        beanFactoryMock.when(BeanFactory::getInstance).thenReturn(beanFactory);

        S3OrangeFileConfig s3OrangeFileConfig = mock(S3OrangeFileConfig.class);
        when(s3OrangeFileConfig.getBucketName()).thenReturn("bucket");
        s3OrangeFileConfigMock = mockStatic(S3OrangeFileConfig.class);
        s3OrangeFileConfigMock.when(S3OrangeFileConfig::getInstance).thenReturn(s3OrangeFileConfig);
    }

    @AfterAll
    public static void tearDownClass() {
        loggerFactoryMock.close();
        beanFactoryMock.close();
        s3OrangeFileConfigMock.close();
    }

    @AfterEach
    public void tearDown() {
        if (ioUtilsMock != null) {
            ioUtilsMock.close();
            ioUtilsMock = null;
        }
    }

    @BeforeEach
    public void setUp() {
        zipCreator = mock(ZipCreator.class);
        amazonS3Client = mock(AmazonS3Client.class);
        when(beanFactory.getZipCreator()).thenReturn(zipCreator);
        when(beanFactory.getAmazonS3()).thenReturn(amazonS3Client);
        bundleGenerationHandler = new BundleGenerationHandler();
    }

    @Test
    public void handleRequest_orangeFileGenerated_pdfFile() throws IOException {
        // arrange
        String downloadUri = "http://localhost/fileName.zip.or.pdf";
        APIGatewayProxyRequestEvent requestEvent = new APIGatewayProxyRequestEvent();
        requestEvent.setPathParameters(Collections.singletonMap("orderId", ORDER_ID + "/"));
        ObjectListing objectList = new ObjectListing();
        objectList.setBucketName("orangeBucket");
        requestEvent.setPath("/orange/");

        S3ObjectSummary s3ObjectSummary = new S3ObjectSummary();
        s3ObjectSummary.setKey(ORDER_ID + "/fileName.zip.or.pdf");
        objectList.getObjectSummaries().add(s3ObjectSummary);

        ListObjectsV2Result listObjectsV2Result = new ListObjectsV2Result();
        listObjectsV2Result.setKeyCount(6);

        //IOUtils
        ioUtilsMock = mockStatic(IOUtils.class);
        ioUtilsMock.when(() -> IOUtils.toByteArray(any())).thenReturn("orange file".getBytes());

        when(amazonS3Client.listObjectsV2(any(), any())).thenReturn(listObjectsV2Result);
        when(amazonS3Client.listObjects(any(), any())).thenReturn(objectList);
        when(amazonS3Client.getObject(anyString(), any())).thenReturn(new S3Object());

        // act
        APIGatewayProxyResponseEvent responseEvent = bundleGenerationHandler.handleRequest(requestEvent, null);
        // assert
        assertEquals(HttpStatus.SC_OK, responseEvent.getStatusCode().intValue(), "HTTP status mismatch");
        assertNotNull(responseEvent.getBody());
        assertNotNull(responseEvent.getHeaders().get(HttpHeaders.CONTENT_TYPE));
        assertEquals("application/pdf", responseEvent.getHeaders().get(HttpHeaders.CONTENT_TYPE));
    }

    @Test
    public void handleRequest_orangeFileGenerated_zipFile() throws IOException {
        // arrange
        String downloadUri = "http://var/folders/p0/_lp72kb10k73p100fqbp49s40000gn/T/myOrder3709055078889086581.zip";
        APIGatewayProxyRequestEvent requestEvent = new APIGatewayProxyRequestEvent();
        requestEvent.setPathParameters(Collections.singletonMap("orderId", ORDER_ID));
        requestEvent.setPath("/orange/");
        ObjectListing objectList = new ObjectListing();
        objectList.setBucketName("orangeBucket");

        S3ObjectSummary s3ObjectSummary1 = new S3ObjectSummary();
        s3ObjectSummary1.setKey(ORDER_ID + "1" + "/" + ".pdf");

        S3ObjectSummary s3ObjectSummary2 = new S3ObjectSummary();
        s3ObjectSummary2.setKey(ORDER_ID + "2" + "/" + ".pdf");

        objectList.getObjectSummaries().add(s3ObjectSummary1);
        objectList.getObjectSummaries().add(s3ObjectSummary2);

        ListObjectsV2Result listObjectsV2Result = new ListObjectsV2Result();
        listObjectsV2Result.setKeyCount(6);
        byte[] bytes = "orange file".getBytes();

        //IOUtils
        ioUtilsMock = mockStatic(IOUtils.class);
        ioUtilsMock.when(() -> IOUtils.toByteArray(any())).thenReturn(bytes);

        when(amazonS3Client.listObjectsV2(any(), any())).thenReturn(listObjectsV2Result);
        when(amazonS3Client.listObjects(any(), any())).thenReturn(objectList);
        when(zipCreator.bundleZip(any())).thenReturn(bytes);
        when(amazonS3Client.getObject(anyString(), any())).thenReturn(new S3Object());

        // act
        APIGatewayProxyResponseEvent responseEvent = bundleGenerationHandler.handleRequest(requestEvent, null);
        // assert
        assertEquals(HttpStatus.SC_OK, responseEvent.getStatusCode().intValue(), "HTTP status mismatch");
        assertNotNull(responseEvent.getBody());
        assertNotNull(responseEvent.getHeaders().get(HttpHeaders.CONTENT_TYPE));
        assertEquals("application/zip", responseEvent.getHeaders().get(HttpHeaders.CONTENT_TYPE));
    }

    @Test
    public void handleRequest_errorFileGenerated_txtFile() {
        // arrange
        APIGatewayProxyRequestEvent requestEvent = new APIGatewayProxyRequestEvent();
        requestEvent.setPathParameters(Collections.singletonMap("orderId", ORDER_ID + "/"));
        requestEvent.setPath("/orange/");

        ListObjectsV2Result listObjectsV2Result = new ListObjectsV2Result();
        listObjectsV2Result.setKeyCount(0);
        when(amazonS3Client.listObjectsV2(any(), any())).thenReturn(listObjectsV2Result);

        // act
        APIGatewayProxyResponseEvent responseEvent = bundleGenerationHandler.handleRequest(requestEvent, null);

        // assert
        assertEquals(HttpStatus.SC_NOT_FOUND, responseEvent.getStatusCode().intValue(), "HTTP status mismatch");
        assertNotNull(responseEvent.getBody());
        assertNotNull(responseEvent.getHeaders().get(HttpHeaders.CONTENT_TYPE));
        assertEquals("text/plain", responseEvent.getHeaders().get(HttpHeaders.CONTENT_TYPE));
    }
}

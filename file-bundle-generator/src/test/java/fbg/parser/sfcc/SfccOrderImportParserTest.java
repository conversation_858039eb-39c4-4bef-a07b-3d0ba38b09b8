package fbg.parser.sfcc;

import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import fbg.configuration.FileType;
import fbg.parser.ArchiveParseException;
import org.apache.commons.io.IOUtils;
import org.apache.http.client.methods.HttpGet;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

/**
 * Test class for {@link SfccOrderImportParser}.
 */
@RunWith(MockitoJUnitRunner.class)
public class SfccOrderImportParserTest {
    private SfccOrderImportParser sfccOrderImportParser;

    @Mock
    private S3Object s3Object;

    @Before
    public void setUp() {
        sfccOrderImportParser = new SfccOrderImportParser();
    }

    @Test
    public void parse_validXML_orderIdReturned() throws IOException {
        // arrange
        String validXML = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"
                + "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" "
                + "xmlns:ns=\"http://www.demandware.com/xml/impex/order/2006-10-31\">\n"
                + "<soapenv:Header/>\n"
                + "<soapenv:Body>\n"
                + "<orders xmlns=\"http://www.demandware.com/xml/impex/order/2006-10-31\">\n"
                + "    <order order-no=\"9404283444\">\n"
                + "        <order-date>2018-01-06T10:21:07.000Z</order-date>\n"
                + "        <created-by>storefront</created-by>\n"
                + "    </order>\n"
                + "</orders>\n"
                + "</soapenv:Body>\n"
                + "</soapenv:Envelope>";
        when(s3Object.getObjectContent())
                .thenReturn(new S3ObjectInputStream(IOUtils.toInputStream(validXML, StandardCharsets.UTF_8.name()), new HttpGet()));

        // act
        Set<String> orderIdSet = sfccOrderImportParser.parse(s3Object);

        // assert
        assertEquals(1, orderIdSet.size());
        assertEquals("9404283444", orderIdSet.iterator().next());
    }

    @Test(expected = ArchiveParseException.class)
    public void parse_invalidXML_exceptionIsThrown() throws IOException {
        // arrange
        String invalidXML = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"
                + "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\""
                + " xmlns:ns=\"http://www.demandware.com/xml/impex/order/2006-10-31\">\n"
                + "<soapenv:Header/>\n"
                + "<soapenv:Body>\n"
                + "<orders xmlns=\"http://www.demandware.com/xml/impex/order/2006-10-31\">\n"
                + "    <order>\n"
                + "        <order-date>2018-01-06T10:21:07.000Z</order-date>\n"
                + "        <created-by>storefront</created-by>\n"
                + "    </order>\n"
                + "</orders>\n"
                + "</soapenv:Body>\n"
                + "</soapenv:Envelope>";
        when(s3Object.getObjectContent())
                .thenReturn(new S3ObjectInputStream(IOUtils.toInputStream(invalidXML, StandardCharsets.UTF_8.name()), new HttpGet()));

        // act
        sfccOrderImportParser.parse(s3Object);
    }

    @Test
    public void getArchiveType_methodCalled_returnsArchiveType() {
        assertEquals(FileType.ORDER, sfccOrderImportParser.getArchiveType());
    }
}

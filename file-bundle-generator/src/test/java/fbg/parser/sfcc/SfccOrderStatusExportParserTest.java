package fbg.parser.sfcc;

import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import fbg.configuration.FileType;
import org.apache.commons.io.IOUtils;
import org.apache.http.client.methods.HttpGet;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

/**
 * Test class for {@link SfccOrderStatusExportParser}.
 */
@RunWith(MockitoJUnitRunner.class)
public class SfccOrderStatusExportParserTest {

    private SfccOrderStatusExportParser sfccOrderStatusExportParser;
    @Mock
    private S3Object s3Object;

    @Before
    public void setUp() {
        sfccOrderStatusExportParser = new SfccOrderStatusExportParser();
    }

    @Test
    public void parse_validXML_orderIdsReturned() throws IOException {
        // arrange
        String[] expectedOrdersAsArray = new String[]{"9106707895", "9106708171", "9106710179", "9106711123",
                "9106712141", "9106713647", "9106713927", "9106714021", "9106714361", "9106715107", "9106715358",
                "9106719899", "9106719904", "9106719910", "9106719914", "9106719915", "9106719976", "9106719983",
                "9206719974", "9306708223", "9306708291", "9306708328", "9406719916", "9406719970", "9406719982",
                "9506719907", "9506719911", "9506719917", "9506719972", "9506719975", "9506719978", "9506719980"};
        String validXml = IOUtils.toString(SfccOrderStatusExportParserTest.class
                .getResourceAsStream("/fbg/parser/sfcc/DMW-OrderStatus-20181011002801087.xml"), Charset.forName("utf-8"));
        when(s3Object.getObjectContent()).thenReturn(
                new S3ObjectInputStream(
                        IOUtils.toInputStream(validXml, StandardCharsets.UTF_8.name()), new HttpGet()));

        // act
        Set<String> actualOrders = sfccOrderStatusExportParser.parse(s3Object);

        // assert
        Set<String> expectedOrders = new HashSet<>(Arrays.asList(expectedOrdersAsArray));
        Set<String> expectedOrdersCopy = new HashSet<>(expectedOrders);
        expectedOrders.removeAll(actualOrders);
        actualOrders.removeAll(expectedOrdersCopy);
        assertTrue("Orders were not parsed: " + expectedOrders, expectedOrders.isEmpty());
        assertTrue("Unexpected orders found: " + actualOrders, actualOrders.isEmpty());
    }

    @Test
    public void getArchiveType_methodCalled_returnsArchiveType() {

        // act assert
        assertEquals(FileType.ORDER_STATUS, sfccOrderStatusExportParser.getArchiveType());
    }

}

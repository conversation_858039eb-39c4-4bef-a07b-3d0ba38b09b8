package fbg.parser.hermes;

import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import fbg.configuration.FileType;
import org.apache.commons.io.IOUtils;
import org.apache.http.client.methods.HttpGet;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

/**
 * Test class for {@link HermesOrderFaktParser}.
 */
@RunWith(MockitoJUnitRunner.class)
public class HermesOrderFaktParserTest {
    private HermesOrderFaktParser hermesOrderFaktParser;

    @Mock
    private S3Object s3Object;

    @Before
    public void setUp() {
        hermesOrderFaktParser = new HermesOrderFaktParser();
    }

    @Test
    public void createFaktParser() {
    }

    @Test
    public void createPodParser() {
    }

    @Test
    public void parse_validFAKTLine_orderIdReturned() throws IOException {
        // arrange
        String validFAKTRow = "9203653001\t96890575\t2017.11.06\t12863574\t0\t1\tLI\t1357107\t0"
                + "\tOberteil mit kurze\t1\t\t\t91238808\t5713722565676\t1\t1\t13150788\t\tD\t0.00\t\t0\t4001\t\t2017.11.06\t40\t99\t";
        when(s3Object.getObjectContent()).thenReturn(
                new S3ObjectInputStream(IOUtils.toInputStream(validFAKTRow, StandardCharsets.UTF_8.name()), new HttpGet()));

        // act
        Set<String> orderIdSet = hermesOrderFaktParser.parse(s3Object);

        // assert
        assertEquals(1, orderIdSet.size());
        assertEquals("9203653001", orderIdSet.iterator().next());
    }

    @Test
    public void getArchiveType_methodCalledForFAKT_returnsArchiveType() {
        assertEquals(FileType.FAKT, hermesOrderFaktParser.getArchiveType());
    }

    @Test
    public void parse_s3ObjectCloseThrowsAnException_shouldCatchTheException() throws IOException {
        // arrange
        doThrow(IOException.class).when(s3Object).close();

        // act
        parse_validFAKTLine_orderIdReturned();
    }

    @Test
    public void parse_invalidFAKTLine_shouldCatchTheException() throws IOException {
        // arrange
        String invalidFAKTRow = "\t\t96890575\t2017.11.06\t12863574\t0\t1\tLI\t1357107\t0"
                + "\tOberteil mit kurze\t1\t\t\t91238808\t5713722565676\t1\t1\t13150788\t\tD\t0.00\t\t0\t4001\t\t2017.11.06\t40\t99\t";
        when(s3Object.getObjectContent()).thenReturn(
                new S3ObjectInputStream(IOUtils.toInputStream(invalidFAKTRow, StandardCharsets.UTF_8.name()), new HttpGet()));

        // act
        Set<String> orderIdSet = hermesOrderFaktParser.parse(s3Object);

        // assert
        assertEquals(0, orderIdSet.size());
    }
}
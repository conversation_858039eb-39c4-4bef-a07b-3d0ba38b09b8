package fbg.parser.hermes;

import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import fbg.configuration.FileType;
import org.apache.commons.io.IOUtils;
import org.apache.http.client.methods.HttpGet;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

/**
 * Test class for {@link HermesOrderReturnParser}.
 */
@RunWith(MockitoJUnitRunner.class)
public class HermesOrderReturnParserTest {
    private HermesOrderReturnParser hermesOrderReturnParser;

    @Mock
    private S3Object s3Object;

    @Before
    public void setUp() {
        hermesOrderReturnParser = new HermesOrderReturnParser();
    }

    @Test
    public void parse_validLine_orderIdReturned() throws IOException {
        // arrange
        String validRow = "40\t72726177\tT\t17309416\t10\t6990590\tB\t4995\t1499\t0\t0\t0\t0\t0\t0\t0\t3\t0"
                + "\t0\t0\t4005\tEUR\t10655645\t2105289359\t10\t1\tN\t13\t2\tY";
        when(s3Object.getObjectContent()).thenReturn(
                new S3ObjectInputStream(IOUtils.toInputStream(validRow,  StandardCharsets.UTF_8.name()), new HttpGet()));

        // act
        Set<String> orderIdSet = hermesOrderReturnParser.parse(s3Object);

        // assert
        assertEquals(1, orderIdSet.size());
        assertEquals("2105289359", orderIdSet.iterator().next());
    }

    @Test
    public void parse_invalidLine_orderIdNotReturned() throws IOException {
        // arrange
        String invalidRow = "40\t72726176\tT\t17309416\t10\t6990590\tB\t4995\t1499\t0\t0\t0\t0\t0\t0\t0\t3\t0"
                + "\t0\t0\t4005\tEUR\t10655645";
        when(s3Object.getObjectContent()).thenReturn(
                new S3ObjectInputStream(IOUtils.toInputStream(invalidRow,  StandardCharsets.UTF_8.name()), new HttpGet()));

        // act
        Set<String> orderIdSet = hermesOrderReturnParser.parse(s3Object);

        // assert
        assertTrue(orderIdSet.isEmpty());
    }

    @Test
    public void parse_validFooter_orderIdNotReturned() throws IOException {
        // arrange
        String validFooter = "FOOTER\tt_returnsum\t20101218\t1\t1";
        when(s3Object.getObjectContent()).thenReturn(
                new S3ObjectInputStream(IOUtils.toInputStream(validFooter,  StandardCharsets.UTF_8.name()), new HttpGet()));

        // act
        Set<String> orderIdSet = hermesOrderReturnParser.parse(s3Object);

        // assert
        assertTrue(orderIdSet.isEmpty());
    }

    @Test
    public void getArchiveType_methodCalled_returnsArchiveType() {
        assertEquals(FileType.RETURN, hermesOrderReturnParser.getArchiveType());
    }
}

package fbg.parser.hermes;

import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import fbg.configuration.FileType;
import fbg.parser.ArchiveParseException;
import org.apache.commons.io.IOUtils;
import org.apache.http.client.methods.HttpGet;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

/**
 * test class for {@link HermesOrderExportParser}.
 */
@ExtendWith(MockitoExtension.class)
public class HermesOrderExportParserTest {

    private static final String VALID_XML_TWO_ORDERS = "<amos_orderlist xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" "
            + "xsi:noNamespaceSchemaLocation=\"amos_orderlist_import.xsd\">\n"
            + "\n"
            + "    <amos_order>\n"
            + "        <p_customer>\n"
            + "            <i_company>40</i_company>\n"
            + "            <s_title>Mrs.</s_title>\n"
            + "            <s_name>Aaaaa</s_name>\n"
            + "            <s_firstname>Bbbbb</s_firstname>\n"
            + "            <s_city>Cccccc</s_city>\n"
            + "            <s_address1>Dddddd 11b</s_address1>\n"
            + "            <i_zipcode>4610</i_zipcode>\n"
            + "            <s_phoneno1>+4915199999999</s_phoneno1>\n"
            + "            <s_email><EMAIL></s_email>\n"
            + "            <s_extern_custno>94566905</s_extern_custno>\n"
            + "            <i_country>276</i_country>\n"
            + "            <i_cust_company>4005</i_cust_company>\n"
            + "        </p_customer>\n"
            + "        <p_shipto>\n"
            + "            <i_company>40</i_company>\n"
            + "            <s_name>Aaaaa</s_name>\n"
            + "            <s_firstname>Bbbbb</s_firstname>\n"
            + "            <s_city>Cccccc</s_city>\n"
            + "            <s_address1>Dddddd 11b</s_address1>\n"
            + "            <i_zipcode>4610</i_zipcode>\n"
            + "            <s_phoneno1>+4915199999999</s_phoneno1>\n"
            + "            <i_country>276</i_country>\n"
            + "        </p_shipto>\n"
            + "        <e_ordsum>\n"
            + "            <i_company>40</i_company>\n"
            + "            <f_orderval>65.42</f_orderval>\n"
            + "            <c_pandp>Y</c_pandp>\n"
            + "            <f_pandpval>0.0</f_pandpval>\n"
            + "            <c_ordertype>E</c_ordertype>\n"
            + "            <c_shipto>R</c_shipto>\n"
            + "            <c_delivery>0</c_delivery>\n"
            + "            <c_pandptax>Y</c_pandptax>\n"
            + "            <i_pandp_taxcode>3</i_pandp_taxcode>\n"
            + "            <s_extern_orderno>9404170000</s_extern_orderno>\n"
            + "            <i_carrier>5</i_carrier>\n"
            + "            <s_extern_orderno_long>9404170000</s_extern_orderno_long>\n"
            + "            <d_order>2018-04-02 09:52:03</d_order>\n"
            + "        </e_ordsum>\n"
            + "        <e_orddet>\n"
            + "            <i_company>40</i_company>\n"
            + "            <i_detail>1</i_detail>\n"
            + "            <s_extern_itemno>5713731799680</s_extern_itemno>\n"
            + "            <f_retailprice>19.99</f_retailprice>\n"
            + "            <i_orderqty>1</i_orderqty>\n"
            + "            <f_discountval>0.0</f_discountval>\n"
            + "            <i_taxcode>3</i_taxcode>\n"
            + "            <c_itemtype>N</c_itemtype>\n"
            + "        </e_orddet>\n"
            + "        <e_orddet>\n"
            + "            <i_company>40</i_company>\n"
            + "            <i_detail>4</i_detail>\n"
            + "            <s_extern_itemno>5713449670745</s_extern_itemno>\n"
            + "            <f_retailprice>10.45</f_retailprice>\n"
            + "            <i_orderqty>1</i_orderqty>\n"
            + "            <f_discountval>0.0</f_discountval>\n"
            + "            <i_taxcode>3</i_taxcode>\n"
            + "            <c_itemtype>N</c_itemtype>\n"
            + "        </e_orddet>\n"
            + "        <e_orddet>\n"
            + "            <i_company>40</i_company>\n"
            + "            <i_detail>2</i_detail>\n"
            + "            <s_extern_itemno>5713731801079</s_extern_itemno>\n"
            + "            <f_retailprice>19.99</f_retailprice>\n"
            + "            <i_orderqty>1</i_orderqty>\n"
            + "            <f_discountval>0.0</f_discountval>\n"
            + "            <i_taxcode>3</i_taxcode>\n"
            + "            <c_itemtype>N</c_itemtype>\n"
            + "        </e_orddet>\n"
            + "        <e_orddet>\n"
            + "            <i_company>40</i_company>\n"
            + "            <i_detail>3</i_detail>\n"
            + "            <s_extern_itemno>5713733261864</s_extern_itemno>\n"
            + "            <f_retailprice>14.99</f_retailprice>\n"
            + "            <i_orderqty>1</i_orderqty>\n"
            + "            <f_discountval>0.0</f_discountval>\n"
            + "            <i_taxcode>3</i_taxcode>\n"
            + "            <c_itemtype>N</c_itemtype>\n"
            + "        </e_orddet>\n"
            + "        <p_ordsum_4print>\n"
            + "            <s_paymode>INVOICE</s_paymode>\n"
            + "        </p_ordsum_4print>\n"
            + "        <p_orddet_4print>\n"
            + "            <i_detail>1</i_detail>\n"
            + "        </p_orddet_4print>\n"
            + "        <p_orddet_4print>\n"
            + "            <i_detail>4</i_detail>\n"
            + "        </p_orddet_4print>\n"
            + "        <p_orddet_4print>\n"
            + "            <i_detail>2</i_detail>\n"
            + "        </p_orddet_4print>\n"
            + "        <p_orddet_4print>\n"
            + "            <i_detail>3</i_detail>\n"
            + "        </p_orddet_4print>\n"
            + "        <e_ordsum_attrib>\n"
            + "            <i_attrib>3</i_attrib>\n"
            + "            <s_value>ON</s_value>\n"
            + "        </e_ordsum_attrib>\n"
            + "        <e_ordsum_attrib>\n"
            + "            <i_attrib>5</i_attrib>\n"
            + "            <s_value>8</s_value>\n"
            + "        </e_ordsum_attrib>\n"
            + "    </amos_order>    <amos_order>\n"
            + "        <p_customer>\n"
            + "            <i_company>40</i_company>\n"
            + "            <s_title>Mrs.</s_title>\n"
            + "            <s_name>Aaaaa</s_name>\n"
            + "            <s_firstname>Bbbbb</s_firstname>\n"
            + "            <s_city>Cccccc</s_city>\n"
            + "            <s_address1>Dddddd 11b</s_address1>\n"
            + "            <i_zipcode>4610</i_zipcode>\n"
            + "            <s_phoneno1>+4915199999999</s_phoneno1>\n"
            + "            <s_email><EMAIL></s_email>\n"
            + "            <s_extern_custno>94566905</s_extern_custno>\n"
            + "            <i_country>276</i_country>\n"
            + "            <i_cust_company>4005</i_cust_company>\n"
            + "        </p_customer>\n"
            + "        <p_shipto>\n"
            + "            <i_company>40</i_company>\n"
            + "            <s_name>Aaaaa</s_name>\n"
            + "            <s_firstname>Bbbbb</s_firstname>\n"
            + "            <s_city>Cccccc</s_city>\n"
            + "            <s_address1>Dddddd 11b</s_address1>\n"
            + "            <i_zipcode>4610</i_zipcode>\n"
            + "            <s_phoneno1>+4915199999999</s_phoneno1>\n"
            + "            <i_country>276</i_country>\n"
            + "        </p_shipto>\n"
            + "        <e_ordsum>\n"
            + "            <i_company>40</i_company>\n"
            + "            <f_orderval>65.42</f_orderval>\n"
            + "            <c_pandp>Y</c_pandp>\n"
            + "            <f_pandpval>0.0</f_pandpval>\n"
            + "            <c_ordertype>E</c_ordertype>\n"
            + "            <c_shipto>R</c_shipto>\n"
            + "            <c_delivery>0</c_delivery>\n"
            + "            <c_pandptax>Y</c_pandptax>\n"
            + "            <i_pandp_taxcode>3</i_pandp_taxcode>\n"
            + "            <s_extern_orderno>8204170001</s_extern_orderno>\n"
            + "            <i_carrier>5</i_carrier>\n"
            + "            <s_extern_orderno_long>8204170001</s_extern_orderno_long>\n"
            + "            <d_order>2018-04-02 09:52:03</d_order>\n"
            + "        </e_ordsum>\n"
            + "        <e_orddet>\n"
            + "            <i_company>40</i_company>\n"
            + "            <i_detail>1</i_detail>\n"
            + "            <s_extern_itemno>5713731799680</s_extern_itemno>\n"
            + "            <f_retailprice>19.99</f_retailprice>\n"
            + "            <i_orderqty>1</i_orderqty>\n"
            + "            <f_discountval>0.0</f_discountval>\n"
            + "            <i_taxcode>3</i_taxcode>\n"
            + "            <c_itemtype>N</c_itemtype>\n"
            + "        </e_orddet>\n"
            + "        <e_orddet>\n"
            + "            <i_company>40</i_company>\n"
            + "            <i_detail>4</i_detail>\n"
            + "            <s_extern_itemno>5713449670745</s_extern_itemno>\n"
            + "            <f_retailprice>10.45</f_retailprice>\n"
            + "            <i_orderqty>1</i_orderqty>\n"
            + "            <f_discountval>0.0</f_discountval>\n"
            + "            <i_taxcode>3</i_taxcode>\n"
            + "            <c_itemtype>N</c_itemtype>\n"
            + "        </e_orddet>\n"
            + "        <e_orddet>\n"
            + "            <i_company>40</i_company>\n"
            + "            <i_detail>2</i_detail>\n"
            + "            <s_extern_itemno>5713731801079</s_extern_itemno>\n"
            + "            <f_retailprice>19.99</f_retailprice>\n"
            + "            <i_orderqty>1</i_orderqty>\n"
            + "            <f_discountval>0.0</f_discountval>\n"
            + "            <i_taxcode>3</i_taxcode>\n"
            + "            <c_itemtype>N</c_itemtype>\n"
            + "        </e_orddet>\n"
            + "        <e_orddet>\n"
            + "            <i_company>40</i_company>\n"
            + "            <i_detail>3</i_detail>\n"
            + "            <s_extern_itemno>5713733261864</s_extern_itemno>\n"
            + "            <f_retailprice>14.99</f_retailprice>\n"
            + "            <i_orderqty>1</i_orderqty>\n"
            + "            <f_discountval>0.0</f_discountval>\n"
            + "            <i_taxcode>3</i_taxcode>\n"
            + "            <c_itemtype>N</c_itemtype>\n"
            + "        </e_orddet>\n"
            + "        <p_ordsum_4print>\n"
            + "            <s_paymode>INVOICE</s_paymode>\n"
            + "        </p_ordsum_4print>\n"
            + "        <p_orddet_4print>\n"
            + "            <i_detail>1</i_detail>\n"
            + "        </p_orddet_4print>\n"
            + "        <p_orddet_4print>\n"
            + "            <i_detail>4</i_detail>\n"
            + "        </p_orddet_4print>\n"
            + "        <p_orddet_4print>\n"
            + "            <i_detail>2</i_detail>\n"
            + "        </p_orddet_4print>\n"
            + "        <p_orddet_4print>\n"
            + "            <i_detail>3</i_detail>\n"
            + "        </p_orddet_4print>\n"
            + "        <e_ordsum_attrib>\n"
            + "            <i_attrib>3</i_attrib>\n"
            + "            <s_value>ON</s_value>\n"
            + "        </e_ordsum_attrib>\n"
            + "        <e_ordsum_attrib>\n"
            + "            <i_attrib>5</i_attrib>\n"
            + "            <s_value>8</s_value>\n"
            + "        </e_ordsum_attrib>\n"
            + "    </amos_order>\n"
            + "</amos_orderlist>";

    private static final String VALID_XML_ONE_ORDER = "<amos_orderlist xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" "
            + "xsi:noNamespaceSchemaLocation=\"amos_orderlist_import.xsd\">\n"
            + "\n"
            + "    <amos_order>\n"
            + "        <p_customer>\n"
            + "            <i_company>40</i_company>\n"
            + "            <s_title>Mrs.</s_title>\n"
            + "            <s_name>Aaaaa</s_name>\n"
            + "            <s_firstname>Bbbbb</s_firstname>\n"
            + "            <s_city>Cccccc</s_city>\n"
            + "            <s_address1>Dddddd 11b</s_address1>\n"
            + "            <i_zipcode>4610</i_zipcode>\n"
            + "            <s_phoneno1>+4915199999999</s_phoneno1>\n"
            + "            <s_email><EMAIL></s_email>\n"
            + "            <s_extern_custno>94566905</s_extern_custno>\n"
            + "            <i_country>276</i_country>\n"
            + "            <i_cust_company>4005</i_cust_company>\n"
            + "        </p_customer>\n"
            + "        <p_shipto>\n"
            + "            <i_company>40</i_company>\n"
            + "            <s_name>Aaaaa</s_name>\n"
            + "            <s_firstname>Bbbbb</s_firstname>\n"
            + "            <s_city>Cccccc</s_city>\n"
            + "            <s_address1>Dddddd 11b</s_address1>\n"
            + "            <i_zipcode>4610</i_zipcode>\n"
            + "            <s_phoneno1>+4915199999999</s_phoneno1>\n"
            + "            <i_country>276</i_country>\n"
            + "        </p_shipto>\n"
            + "        <e_ordsum>\n"
            + "            <i_company>40</i_company>\n"
            + "            <f_orderval>65.42</f_orderval>\n"
            + "            <c_pandp>Y</c_pandp>\n"
            + "            <f_pandpval>0.0</f_pandpval>\n"
            + "            <c_ordertype>E</c_ordertype>\n"
            + "            <c_shipto>R</c_shipto>\n"
            + "            <c_delivery>0</c_delivery>\n"
            + "            <c_pandptax>Y</c_pandptax>\n"
            + "            <i_pandp_taxcode>3</i_pandp_taxcode>\n"
            + "            <s_extern_orderno>9404170000</s_extern_orderno>\n"
            + "            <i_carrier>5</i_carrier>\n"
            + "            <s_extern_orderno_long>9404170000</s_extern_orderno_long>\n"
            + "            <d_order>2018-04-02 09:52:03</d_order>\n"
            + "        </e_ordsum>\n"
            + "        <e_orddet>\n"
            + "            <i_company>40</i_company>\n"
            + "            <i_detail>1</i_detail>\n"
            + "            <s_extern_itemno>5713731799680</s_extern_itemno>\n"
            + "            <f_retailprice>19.99</f_retailprice>\n"
            + "            <i_orderqty>1</i_orderqty>\n"
            + "            <f_discountval>0.0</f_discountval>\n"
            + "            <i_taxcode>3</i_taxcode>\n"
            + "            <c_itemtype>N</c_itemtype>\n"
            + "        </e_orddet>\n"
            + "        <e_orddet>\n"
            + "            <i_company>40</i_company>\n"
            + "            <i_detail>4</i_detail>\n"
            + "            <s_extern_itemno>5713449670745</s_extern_itemno>\n"
            + "            <f_retailprice>10.45</f_retailprice>\n"
            + "            <i_orderqty>1</i_orderqty>\n"
            + "            <f_discountval>0.0</f_discountval>\n"
            + "            <i_taxcode>3</i_taxcode>\n"
            + "            <c_itemtype>N</c_itemtype>\n"
            + "        </e_orddet>\n"
            + "        <e_orddet>\n"
            + "            <i_company>40</i_company>\n"
            + "            <i_detail>2</i_detail>\n"
            + "            <s_extern_itemno>5713731801079</s_extern_itemno>\n"
            + "            <f_retailprice>19.99</f_retailprice>\n"
            + "            <i_orderqty>1</i_orderqty>\n"
            + "            <f_discountval>0.0</f_discountval>\n"
            + "            <i_taxcode>3</i_taxcode>\n"
            + "            <c_itemtype>N</c_itemtype>\n"
            + "        </e_orddet>\n"
            + "        <e_orddet>\n"
            + "            <i_company>40</i_company>\n"
            + "            <i_detail>3</i_detail>\n"
            + "            <s_extern_itemno>5713733261864</s_extern_itemno>\n"
            + "            <f_retailprice>14.99</f_retailprice>\n"
            + "            <i_orderqty>1</i_orderqty>\n"
            + "            <f_discountval>0.0</f_discountval>\n"
            + "            <i_taxcode>3</i_taxcode>\n"
            + "            <c_itemtype>N</c_itemtype>\n"
            + "        </e_orddet>\n"
            + "        <p_ordsum_4print>\n"
            + "            <s_paymode>INVOICE</s_paymode>\n"
            + "        </p_ordsum_4print>\n"
            + "        <p_orddet_4print>\n"
            + "            <i_detail>1</i_detail>\n"
            + "        </p_orddet_4print>\n"
            + "        <p_orddet_4print>\n"
            + "            <i_detail>4</i_detail>\n"
            + "        </p_orddet_4print>\n"
            + "        <p_orddet_4print>\n"
            + "            <i_detail>2</i_detail>\n"
            + "        </p_orddet_4print>\n"
            + "        <p_orddet_4print>\n"
            + "            <i_detail>3</i_detail>\n"
            + "        </p_orddet_4print>\n"
            + "        <e_ordsum_attrib>\n"
            + "            <i_attrib>3</i_attrib>\n"
            + "            <s_value>ON</s_value>\n"
            + "        </e_ordsum_attrib>\n"
            + "        <e_ordsum_attrib>\n"
            + "            <i_attrib>5</i_attrib>\n"
            + "            <s_value>8</s_value>\n"
            + "        </e_ordsum_attrib>\n"
            + "    </amos_order>\n"
            + "</amos_orderlist>";

    private static final String EMPTY_ORDER_ID = "<amos_orderlist xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" "
            + "xsi:noNamespaceSchemaLocation=\"amos_orderlist_import.xsd\">\n"
            + "\n"
            + "    <amos_order>\n"
            + "        <p_customer>\n"
            + "            <i_company>40</i_company>\n"
            + "            <s_title>Mrs.</s_title>\n"
            + "            <s_name>Aaaaa</s_name>\n"
            + "            <s_firstname>Bbbbb</s_firstname>\n"
            + "            <s_city>Cccccc</s_city>\n"
            + "            <s_address1>Dddddd 11b</s_address1>\n"
            + "            <i_zipcode>4610</i_zipcode>\n"
            + "            <s_phoneno1>+4915199999999</s_phoneno1>\n"
            + "            <s_email><EMAIL></s_email>\n"
            + "            <s_extern_custno>94566905</s_extern_custno>\n"
            + "            <i_country>276</i_country>\n"
            + "            <i_cust_company>4005</i_cust_company>\n"
            + "        </p_customer>\n"
            + "        <p_shipto>\n"
            + "            <i_company>40</i_company>\n"
            + "            <s_name>Aaaaa</s_name>\n"
            + "            <s_firstname>Bbbbb</s_firstname>\n"
            + "            <s_city>Cccccc</s_city>\n"
            + "            <s_address1>Dddddd 11b</s_address1>\n"
            + "            <i_zipcode>4610</i_zipcode>\n"
            + "            <s_phoneno1>+4915199999999</s_phoneno1>\n"
            + "            <i_country>276</i_country>\n"
            + "        </p_shipto>\n"
            + "        <e_ordsum>\n"
            + "            <i_company>40</i_company>\n"
            + "            <f_orderval>65.42</f_orderval>\n"
            + "            <c_pandp>Y</c_pandp>\n"
            + "            <f_pandpval>0.0</f_pandpval>\n"
            + "            <c_ordertype>E</c_ordertype>\n"
            + "            <c_shipto>R</c_shipto>\n"
            + "            <c_delivery>0</c_delivery>\n"
            + "            <c_pandptax>Y</c_pandptax>\n"
            + "            <i_pandp_taxcode>3</i_pandp_taxcode>\n"
            + "            <s_extern_orderno></s_extern_orderno>\n"
            + "            <i_carrier>5</i_carrier>\n"
            + "            <s_extern_orderno_long>9404170000</s_extern_orderno_long>\n"
            + "            <d_order>2018-04-02 09:52:03</d_order>\n"
            + "        </e_ordsum>\n"
            + "        <e_orddet>\n"
            + "            <i_company>40</i_company>\n"
            + "            <i_detail>1</i_detail>\n"
            + "            <s_extern_itemno>5713731799680</s_extern_itemno>\n"
            + "            <f_retailprice>19.99</f_retailprice>\n"
            + "            <i_orderqty>1</i_orderqty>\n"
            + "            <f_discountval>0.0</f_discountval>\n"
            + "            <i_taxcode>3</i_taxcode>\n"
            + "            <c_itemtype>N</c_itemtype>\n"
            + "        </e_orddet>\n"
            + "        <e_orddet>\n"
            + "            <i_company>40</i_company>\n"
            + "            <i_detail>4</i_detail>\n"
            + "            <s_extern_itemno>5713449670745</s_extern_itemno>\n"
            + "            <f_retailprice>10.45</f_retailprice>\n"
            + "            <i_orderqty>1</i_orderqty>\n"
            + "            <f_discountval>0.0</f_discountval>\n"
            + "            <i_taxcode>3</i_taxcode>\n"
            + "            <c_itemtype>N</c_itemtype>\n"
            + "        </e_orddet>\n"
            + "        <e_orddet>\n"
            + "            <i_company>40</i_company>\n"
            + "            <i_detail>2</i_detail>\n"
            + "            <s_extern_itemno>5713731801079</s_extern_itemno>\n"
            + "            <f_retailprice>19.99</f_retailprice>\n"
            + "            <i_orderqty>1</i_orderqty>\n"
            + "            <f_discountval>0.0</f_discountval>\n"
            + "            <i_taxcode>3</i_taxcode>\n"
            + "            <c_itemtype>N</c_itemtype>\n"
            + "        </e_orddet>\n"
            + "        <e_orddet>\n"
            + "            <i_company>40</i_company>\n"
            + "            <i_detail>3</i_detail>\n"
            + "            <s_extern_itemno>5713733261864</s_extern_itemno>\n"
            + "            <f_retailprice>14.99</f_retailprice>\n"
            + "            <i_orderqty>1</i_orderqty>\n"
            + "            <f_discountval>0.0</f_discountval>\n"
            + "            <i_taxcode>3</i_taxcode>\n"
            + "            <c_itemtype>N</c_itemtype>\n"
            + "        </e_orddet>\n"
            + "        <p_ordsum_4print>\n"
            + "            <s_paymode>INVOICE</s_paymode>\n"
            + "        </p_ordsum_4print>\n"
            + "        <p_orddet_4print>\n"
            + "            <i_detail>1</i_detail>\n"
            + "        </p_orddet_4print>\n"
            + "        <p_orddet_4print>\n"
            + "            <i_detail>4</i_detail>\n"
            + "        </p_orddet_4print>\n"
            + "        <p_orddet_4print>\n"
            + "            <i_detail>2</i_detail>\n"
            + "        </p_orddet_4print>\n"
            + "        <p_orddet_4print>\n"
            + "            <i_detail>3</i_detail>\n"
            + "        </p_orddet_4print>\n"
            + "        <e_ordsum_attrib>\n"
            + "            <i_attrib>3</i_attrib>\n"
            + "            <s_value>ON</s_value>\n"
            + "        </e_ordsum_attrib>\n"
            + "        <e_ordsum_attrib>\n"
            + "            <i_attrib>5</i_attrib>\n"
            + "            <s_value>8</s_value>\n"
            + "        </e_ordsum_attrib>\n"
            + "    </amos_order>\n"
            + "</amos_orderlist>";

    @Mock
    private S3Object s3Object;

    private HermesOrderExportParser orderExportParser;

    @BeforeEach
    public void setUp() {
        orderExportParser = new HermesOrderExportParser();
    }

    @Test
    public void parse_validImportWithOneOrderId_orderIdReturned() throws IOException {
        // arrange
        when(s3Object.getObjectContent())
                .thenReturn(new S3ObjectInputStream(IOUtils.toInputStream(VALID_XML_ONE_ORDER, StandardCharsets.UTF_8.name()), new HttpGet()));

        //act
        final Set<String> orderList = orderExportParser.parse(s3Object);

        //assert
        assertEquals(1, orderList.size(), "Order list contains one order");
        assertTrue(orderList.contains("9404170000"));
    }

    @Test
    public void parse_validImportWithTwoOrderId_orderIdsReturned() throws IOException {
        // arrange


        when(s3Object.getObjectContent())
                .thenReturn(new S3ObjectInputStream(IOUtils.toInputStream(VALID_XML_TWO_ORDERS, StandardCharsets.UTF_8.name()), new HttpGet()));

        //act
        final Set<String> orderList = orderExportParser.parse(s3Object);

        //assert
        assertEquals(2, orderList.size(), "Order list contains two orders");
        assertTrue(orderList.contains("9404170000"));
        assertTrue(orderList.contains("8204170001"));
    }

    @Test
    public void parse_importWithoutOrderId_listIsEmpty() throws IOException {
        // arrange
        when(s3Object.getObjectContent())
                .thenReturn(new S3ObjectInputStream(IOUtils.toInputStream(EMPTY_ORDER_ID, StandardCharsets.UTF_8.name()), new HttpGet()));

        //act
        final Set<String> orderList = orderExportParser.parse(s3Object);

        //assert
        assertEquals(0, orderList.size(), "Order list contains zero orders");
    }

    @Test
    public void parse_exceptionOnClose_exceptionIsCaught() throws IOException {
        // arrange
        when(s3Object.getObjectContent())
                .thenReturn(new S3ObjectInputStream(IOUtils.toInputStream(VALID_XML_ONE_ORDER, StandardCharsets.UTF_8.name()), new HttpGet()));
        doThrow(IOException.class).when(s3Object).close();

        //act
        final Set<String> orderList = orderExportParser.parse(s3Object);

        //assert
        assertEquals(1, orderList.size(), "Order list contains one order");
        assertTrue(orderList.contains("9404170000"));
    }

    @Test
    public void parse_streamInvalid_exceptionIsHandled() throws IOException {
        // arrange
        when(s3Object.getObjectContent())
                .thenReturn(new S3ObjectInputStream(IOUtils.toInputStream("", StandardCharsets.UTF_8.name()), new HttpGet()));

        //act & assert
        assertThrows(ArchiveParseException.class, () -> {
            orderExportParser.parse(s3Object);
        });
    }

    @Test
    public void getArchiveType_methodCalled_returnsArchiveType() {
        assertEquals(FileType.ORDER_EXPORT, orderExportParser.getArchiveType());
    }
}
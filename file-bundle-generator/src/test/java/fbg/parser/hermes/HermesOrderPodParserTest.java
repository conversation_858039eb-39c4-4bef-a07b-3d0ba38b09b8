package fbg.parser.hermes;

import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import fbg.configuration.FileType;
import org.apache.commons.io.IOUtils;
import org.apache.http.client.methods.HttpGet;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

/**
 * Test class for {@See HermesOrderPodParserTest}.
 */
@RunWith(MockitoJUnitRunner.class)
public class HermesOrderPodParserTest {
    private HermesOrderPodParser hermesOrderPodParser;

    @Mock
    private S3Object s3Object;

    @Before
    public void setUp() throws Exception {
        hermesOrderPodParser = new HermesOrderPodParser();
    }

    @Test
    public void parse_validPODLine_orderIdReturned() throws IOException {
        // arrange
        String validPODRow = "*********\t108675771\t2017.05.05\t11056326\t25480333\t1\tLI\t501774\t908\t"
                + "T-Shirt\t1\tPOSTNL\t3SCDPW0000001\t4589324\t5711881546208\t1\t1\t16034242\t\tK\t19.95\t\t20080\t4005\t"
                + "11229632\t2017.05.06\t5\t\t3SEAPP0000046\t2017.05.10";
        when(s3Object.getObjectContent()).thenReturn(
                new S3ObjectInputStream(IOUtils.toInputStream(validPODRow, StandardCharsets.UTF_8.name()), new HttpGet()));

        // act
        Set<String> orderIdSet = hermesOrderPodParser.parse(s3Object);

        // assert
        assertEquals(1, orderIdSet.size());
        assertEquals("*********", orderIdSet.iterator().next());
    }

    @Test
    public void getArchiveType_methodCalledForPOD_returnsArchiveType() {
        assertEquals(FileType.POD, hermesOrderPodParser.getArchiveType());
    }
}

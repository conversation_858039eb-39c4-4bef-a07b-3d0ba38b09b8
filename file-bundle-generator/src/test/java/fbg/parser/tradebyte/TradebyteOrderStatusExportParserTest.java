package fbg.parser.tradebyte;

import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import fbg.configuration.FileType;
import org.apache.commons.io.IOUtils;
import org.apache.http.client.methods.HttpGet;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

/**
 * Test class for {@link TradebyteOrderStatusExportParser}.
 */
@RunWith(MockitoJUnitRunner.class)
public class TradebyteOrderStatusExportParserTest {
    private TradebyteOrderStatusExportParser tradebyteOrderStatusExportParser;

    @Mock
    private S3Object s3Object;

    @Before
    public void setUp() {
        tradebyteOrderStatusExportParser = new TradebyteOrderStatusExportParser();
    }

    @Test
    public void parse_validXML_orderIdsReturned() throws IOException {
        // arrange
        String[] expectedOrdersAsArray = new String[]{"TB9979391", "TB9979539", "TB9979547", "TB9979549", "TB9979563",
                "TB9979567", "TB9979573", "TB9979577", "TB9980325", "TB9980327", "TB9980329", "TB9980333", "TB9980339",
                "TB9980351", "TB9980355", "TB9980649", "TB9980653", "TB9980657", "TB9980659", "TB9980661", "TB9980663",
                "TB9980665", "TB9980669", "TB9980671", "TB9980711", "TB9980715", "TB9980717", "TB9980865", "TB9980867",
                "TB9980869", "TB9980873", "TB9980875", "TB9980881", "TB9980887", "TB9980891", "TB9980895", "TB9980921",
                "TB9980939", "TB9981059", "TB9981073", "TB9981075", "TB9981079", "TB9981083", "TB9981123", "TB9981267",
                "TB9981275", "TB9981291", "TB9981295", "TB9981333"};
        String validXml = IOUtils.toString(TradebyteOrderStatusExportParserTest.class
                .getResourceAsStream("/fbg/parser/tradebyte/TB-OrderStatus-20181011010003247.xml"), Charset.forName("utf-8"));
        when(s3Object.getObjectContent()).thenReturn(
                new S3ObjectInputStream(
                        IOUtils.toInputStream(validXml, StandardCharsets.UTF_8.name()), new HttpGet()));

        // act
        Set<String> actualOrders = tradebyteOrderStatusExportParser.parse(s3Object);


        // assert
        Set<String> expectedOrders = new HashSet<>(Arrays.asList(expectedOrdersAsArray));
        Set<String> expectedOrdersCopy = new HashSet<>(expectedOrders);
        expectedOrders.removeAll(actualOrders);
        actualOrders.removeAll(expectedOrdersCopy);
        assertTrue("Orders were not parsed: " + expectedOrders, expectedOrders.isEmpty());
        assertTrue("Unexpected orders found: " + actualOrders, actualOrders.isEmpty());
    }

    @Test
    public void getArchiveType_methodCalled_returnsArchiveType() {

        // act assert
        assertEquals(FileType.ORDER_STATUS, tradebyteOrderStatusExportParser.getArchiveType());
    }

}

package fbg.parser.tradebyte;

import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import fbg.configuration.FileType;
import fbg.parser.ArchiveParseException;
import org.apache.commons.io.IOUtils;
import org.apache.http.client.methods.HttpGet;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

/**
 * Test class for {@link TradebyteOrderImportParser}.
 */
@RunWith(MockitoJUnitRunner.class)
public class TradebyteOrderImportParserTest {

    private TradebyteOrderImportParser tradebyteOrderImportParser;

    @Mock
    private S3Object s3Object;

    @Before
    public void setUp() {
        tradebyteOrderImportParser = new TradebyteOrderImportParser();
    }

    @Test
    public void parse_validXML_orderIdReturned() throws IOException {
        // arrange
        String validXML = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"
                + "<ORDER_LIST>\n"
                + "<ORDER xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">\n"
                + "<ORDER_DATA>\n"
                + "<ORDER_DATE>2017-12-27</ORDER_DATE>\n"
                + "<TB_ID>6494419</TB_ID>\n"
                + "</ORDER_DATA>\n"
                + "</ORDER>\n"
                + "</ORDER_LIST>";
        when(s3Object.getObjectContent()).thenReturn(
                new S3ObjectInputStream(IOUtils.toInputStream(validXML,  StandardCharsets.UTF_8.name()), new HttpGet()));

        // act
        Set<String> orderIdSet = tradebyteOrderImportParser.parse(s3Object);

        // assert
        assertEquals(1, orderIdSet.size());
        assertEquals("TB6494419", orderIdSet.iterator().next());
    }

    @Test
    public void parse_invalidXML_orderIdNotReturned() throws IOException {
        // arrange
        String invalidXML = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"
                + "<ORDER_LIST>\n"
                + "<ORDER xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">\n"
                + "<ORDER_DATA>\n"
                + "<ORDER_DATE>2017-12-27</ORDER_DATE>\n"
                + "<TB_ID></TB_ID>\n"
                + "</ORDER_DATA>\n"
                + "</ORDER>\n"
                + "</ORDER_LIST>";
        when(s3Object.getObjectContent()).thenReturn(
                new S3ObjectInputStream(IOUtils.toInputStream(invalidXML,  StandardCharsets.UTF_8.name()), new HttpGet()));

        // act
        Set<String> orderIdSet = tradebyteOrderImportParser.parse(s3Object);

        // assert
        assertTrue(orderIdSet.isEmpty());
    }

    @Test(expected = ArchiveParseException.class)
    public void parse_corruptedXML_exceptionIsThrown() throws IOException {
        // arrange
        String corruptedXML = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"
                + "<ORDER_LIST>\n"
                + "<ORDER xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">\n"
                + "<ORDER_DATE>2017-12-27</ORDER_DATE>\n"
                + "<TB_ID>6494419</TB_ID>\n"
                + "</ORDER_DATA>\n"
                + "</ORDER>\n"
                + "</ORDER_LIST>";
        when(s3Object.getObjectContent()).thenReturn(
                new S3ObjectInputStream(IOUtils.toInputStream(corruptedXML,  StandardCharsets.UTF_8.name()), new HttpGet()));

        // act
        tradebyteOrderImportParser.parse(s3Object);
    }

    @Test
    public void getArchiveType_methodCalled_returnsArchiveType() {
        assertEquals(FileType.ORDER, tradebyteOrderImportParser.getArchiveType());
    }
}

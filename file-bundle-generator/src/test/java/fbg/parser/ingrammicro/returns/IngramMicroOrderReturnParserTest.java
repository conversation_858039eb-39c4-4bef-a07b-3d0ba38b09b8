package fbg.parser.ingrammicro.returns;

import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import fbg.configuration.FileType;
import fbg.parser.ArchiveParseException;
import fbg.parser.sfcc.SfccOrderStatusExportParserTest;
import org.apache.commons.io.IOUtils;
import org.apache.http.client.methods.HttpGet;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

/**
 * Test class for {@link IngramMicroOrderReturnParser}.
 */
@RunWith(MockitoJUnitRunner.class)
public class IngramMicroOrderReturnParserTest {

    private IngramMicroOrderReturnParser ingramMicroOrderReturnParser;

    @Mock
    private S3Object s3Object;

    @Before
    public void setUp() {
        ingramMicroOrderReturnParser = new IngramMicroOrderReturnParser();
    }

    @Test
    public void parse_validXML_orderIdReturned() throws IOException {
        // arrange
        String validJSON = IOUtils.toString(SfccOrderStatusExportParserTest.class
                .getResourceAsStream("/fbg/parser/ingrammicro/returnsRequestPayload.json"), Charset.forName("utf-8"));

        when(s3Object.getObjectContent()).thenReturn(
                new S3ObjectInputStream(IOUtils.toInputStream(validJSON,  StandardCharsets.UTF_8.name()), new HttpGet()));

        // act
        Set<String> orderIdSet = ingramMicroOrderReturnParser.parse(s3Object);

        // assert
        assertEquals(1, orderIdSet.size());
        assertEquals("123", orderIdSet.iterator().next());
    }

    @Test
    public void parse_emptyJSON_orderIdNotReturned() throws IOException {
        // arrange
        String emptyJson = "[{}]";
        when(s3Object.getObjectContent()).thenReturn(
                new S3ObjectInputStream(IOUtils.toInputStream(emptyJson, StandardCharsets.UTF_8.name()), new HttpGet()));

        // act
        Set<String> orderIdSet = ingramMicroOrderReturnParser.parse(s3Object);

        // assert
        assertTrue(orderIdSet.isEmpty());
    }

    @Test(expected = ArchiveParseException.class)
    public void parse_invalidJson_exceptionIsThrown() throws IOException {
        // arrange
        String invalidJson = "test";
        when(s3Object.getObjectContent()).thenReturn(
                new S3ObjectInputStream(IOUtils.toInputStream(invalidJson,  StandardCharsets.UTF_8.name()), new HttpGet()));

        // act
        ingramMicroOrderReturnParser.parse(s3Object);
    }

    @Test
    public void getArchiveType_methodCalled_returnsArchiveType() {
        assertEquals(FileType.RETURN, ingramMicroOrderReturnParser.getArchiveType());
    }
}

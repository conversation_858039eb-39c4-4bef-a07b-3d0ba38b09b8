package fbg.parser.plbc;

import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import fbg.configuration.FileType;
import fbg.parser.ArchiveParseException;
import org.apache.commons.io.IOUtils;
import org.apache.http.client.methods.HttpGet;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ParcelLabPayloadParserTest {

    private ParcelLabPayloadsParser parser;

    @Mock
    private S3Object s3Object;

    @Before
    public void setUp() {
        parser = new ParcelLabPayloadsParser();
    }

    @Test
    public void parse_validJSON_successful() throws IOException {
        // arrange
        String validJSON = IOUtils.toString(ParcelLabPayloadParserTest.class
            .getResourceAsStream("/fbg/parser/plbc/orderPlacementPayload.json"), Charset.forName("utf-8"));

        when(s3Object.getObjectContent()).thenReturn(
            new S3ObjectInputStream(IOUtils.toInputStream(validJSON, StandardCharsets.UTF_8.name()), new HttpGet()));

        // act
        Set<String> orderIdSet = parser.parse(s3Object);

        // assert
        assertEquals(1, orderIdSet.size());
        assertEquals("OL1000020096", orderIdSet.iterator().next());
    }

    @Test
    public void parse_emptyJSON_emptyResponse() throws IOException {
        // arrange
        String emptyJson = "{}";
        when(s3Object.getObjectContent()).thenReturn(
            new S3ObjectInputStream(IOUtils.toInputStream(emptyJson, StandardCharsets.UTF_8.name()), new HttpGet()));

        // act
        Set<String> orderIdSet = parser.parse(s3Object);

        // assert
        assertTrue(orderIdSet.isEmpty());
    }

    @Test(expected = ArchiveParseException.class)
    public void parse_invalidJson_exceptionIsThrown() throws IOException {
        // arrange
        String invalidJson = "test";
        when(s3Object.getObjectContent()).thenReturn(
            new S3ObjectInputStream(IOUtils.toInputStream(invalidJson, StandardCharsets.UTF_8.name()), new HttpGet()));

        // act
        parser.parse(s3Object);
    }

    @Test
    public void getArchiveType_methodCalled_returnsArchiveType() {
        assertEquals(FileType.PARCELLAB_PAYLOADS, parser.getArchiveType());
    }
}

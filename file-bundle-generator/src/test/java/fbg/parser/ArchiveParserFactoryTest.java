package fbg.parser;

import fbg.configuration.HwcConfiguration;
import fbg.configuration.ImwcConfiguration;
import fbg.configuration.OmsConfiguration;
import fbg.configuration.OsimcConfiguration;
import fbg.configuration.PlbcConfiguration;
import fbg.configuration.TbcConfiguration;
import fbg.parser.hermes.HermesOrderExportParser;
import fbg.parser.hermes.HermesOrderReturnParser;
import fbg.parser.hermes.HermesOrderUpdateParser;
import fbg.parser.ingrammicro.returns.IngramMicroOrderReturnParser;
import fbg.parser.plbc.ParcelLabPayloadsParser;
import fbg.parser.sfcc.SfccOrderImportParser;
import fbg.parser.sfcc.SfccOrderStatusExportParser;
import fbg.parser.tradebyte.TradebyteOrderImportParser;
import fbg.parser.tradebyte.TradebyteOrderStatusExportParser;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link ArchiveParserFactory}.
 */
@ExtendWith(MockitoExtension.class)
public class ArchiveParserFactoryTest {

    private static final String OMS_BUCKET_NAME = "omsBucketName";
    private static final String HWC_BUCKET_NAME = "hwcBucketName";
    private static final String IMWC_BUCKET_NAME = "imwcBucketName";
    private static final String TBC_BUCKET_NAME = "tbcBucketName";
    private static final String OSIMC_BUCKET_NAME = "osimcBucketName";
    private static final String PLBC_BUCKET_NAME = "plbcBucketName";

    private static MockedStatic<System> systemMock;
    private static MockedStatic<LoggerFactory> loggerFactoryMock;
    private static MockedStatic<OmsConfiguration> omsConfigMock;
    private static MockedStatic<ImwcConfiguration> imwcConfigMock;
    private static MockedStatic<HwcConfiguration> hwcConfigMock;
    private static MockedStatic<TbcConfiguration> tbcConfigMock;
    private static MockedStatic<OsimcConfiguration> osimcConfigMock;
    private static MockedStatic<PlbcConfiguration> plbcConfigMock;

    private ArchiveParserFactory archiveParserFactory;

    /**
     * Setup environment variables.
     */
    @BeforeAll
    public static void setUpClass() {
        systemMock = mockStatic(System.class);
        systemMock.when(() -> System.getenv("S3_OMS_BUCKET_NAME")).thenReturn(OMS_BUCKET_NAME);
        systemMock.when(() -> System.getenv("S3_HWC_BUCKET_NAME")).thenReturn(HWC_BUCKET_NAME);
        systemMock.when(() -> System.getenv("S3_IMWC_BUCKET_NAME")).thenReturn(IMWC_BUCKET_NAME);
        systemMock.when(() -> System.getenv("S3_TBC_BUCKET_NAME")).thenReturn(TBC_BUCKET_NAME);
        systemMock.when(() -> System.getenv("S3_OSIMC_BUCKET_NAME")).thenReturn(OSIMC_BUCKET_NAME);
        systemMock.when(() -> System.getenv("S3_PLBC_BUCKET_NAME")).thenReturn(PLBC_BUCKET_NAME);

        omsConfigMock = mockStatic(OmsConfiguration.class);
        omsConfigMock.when(() -> OmsConfiguration.getInstance().getBucketName()).thenReturn(OMS_BUCKET_NAME);
        hwcConfigMock = mockStatic(HwcConfiguration.class);
        hwcConfigMock.when(() -> HwcConfiguration.getInstance().getBucketName()).thenReturn(HWC_BUCKET_NAME);
        imwcConfigMock = mockStatic(ImwcConfiguration.class);
        imwcConfigMock.when(() -> ImwcConfiguration.getInstance().getBucketName()).thenReturn(IMWC_BUCKET_NAME);
        tbcConfigMock = mockStatic(TbcConfiguration.class);
        tbcConfigMock.when(() -> TbcConfiguration.getInstance().getBucketName()).thenReturn(TBC_BUCKET_NAME);
        osimcConfigMock = mockStatic(OsimcConfiguration.class);
        osimcConfigMock.when(() -> OsimcConfiguration.getInstance().getBucketName()).thenReturn(OSIMC_BUCKET_NAME);
        plbcConfigMock = mockStatic(PlbcConfiguration.class);
        plbcConfigMock.when(() -> PlbcConfiguration.getInstance().getBucketName()).thenReturn(PLBC_BUCKET_NAME);
    }

    @AfterAll
    public static void tearDownClass() {
        systemMock.close();
        loggerFactoryMock.close();
        omsConfigMock.close();
        imwcConfigMock.close();
        hwcConfigMock.close();
        tbcConfigMock.close();
        osimcConfigMock.close();
        plbcConfigMock.close();
    }

    @BeforeEach
    public void setUp() {
        Logger logger = Mockito.mock(Logger.class);
        loggerFactoryMock = mockStatic(LoggerFactory.class);
        loggerFactoryMock.when(() -> LoggerFactory.getLogger(Mockito.any(Class.class))).thenReturn(logger);

        archiveParserFactory = new ArchiveParserFactory();
    }

    @Test
    public void processS3Object_omsBucketNameAndSfccFolder_returnSfccOrderImportParser() {
        // act
        Optional result = archiveParserFactory.getParser(OMS_BUCKET_NAME, "sfcc-order/");

        // assert
        assertTrue(result.get() instanceof SfccOrderImportParser,
            "OMS bucket SFCC folder: Result is not an instance of SfccOrderImportParser");
    }

    @Test
    public void processS3Object_tbcBucketNameAndTradebyteFolder_returnTradebyteOrderImportParser() {
        // act
        Optional result = archiveParserFactory.getParser(TBC_BUCKET_NAME, "tb-order/");

        // assert
        assertTrue(result.get() instanceof TradebyteOrderImportParser,
            "OMS bucket Tradebyte folder: Result is not an instance of TradebyteOrderImportParser");
    }

    @Test
    public void processS3Object_omsBucketNameAndWrongFolder_throwException() {
        // act & assert
        assertThrows(ArchiveParseException.class, () -> {
            archiveParserFactory.getParser(OMS_BUCKET_NAME, "wrong/");
        });
    }

    @Test
    public void processS3Object_hwcBucketNameAndFaktFolder_returnHermesOrderUpdateParser() {
        // act
        Optional result = archiveParserFactory.getParser(HWC_BUCKET_NAME, "fakt/");

        // assert
        assertTrue(result.get() instanceof HermesOrderUpdateParser,
            "HWC bucket FAKT folder: Result is not an instance of HermesOrderUpdateParser");
    }

    @Test
    public void processS3Object_hwcBucketNameAndPodFolder_returnHermesOrderUpdateParser() {
        // act
        Optional result = archiveParserFactory.getParser(HWC_BUCKET_NAME, "pod/");

        // assert
        assertTrue(result.get() instanceof HermesOrderUpdateParser,
            "HWC bucket POD folder: Result is not an instance of HermesOrderUpdateParser");
    }

    @Test
    public void processS3Object_hwcBucketNameAndReturnsFolder_returnHermesOrderReturnParser() {
        // act
        Optional result = archiveParserFactory.getParser(HWC_BUCKET_NAME, "returns/");

        // assert
        assertTrue(result.get() instanceof HermesOrderReturnParser,
            "HWC bucket Returns folder: Result is not an instance of HermesOrderReturnParser");
    }

    @Test
    public void processS3Object_hwcBucketNameAndWrongFolder_throwException() {
        // act & assert
        assertThrows(ArchiveParseException.class, () -> {
            archiveParserFactory.getParser(HWC_BUCKET_NAME, "wrong/");
        });
    }

    @Test
    public void processS3Object_imwcBucketNameAndFaktPrefix_returnHermesOrderUpdateParser() {
        // act
        Optional result = archiveParserFactory.getParser(IMWC_BUCKET_NAME, "t_orderstat_fakt");

        // assert
        assertTrue(result.get() instanceof HermesOrderUpdateParser,
            "IMWC bucket FAKT folder: Result is not an instance of HermesOrderUpdateParser");
    }

    @Test
    public void processS3Object_imwcBucketNameAndPodPrefix_returnHermesOrderUpdateParser() {
        // act
        Optional result = archiveParserFactory.getParser(IMWC_BUCKET_NAME, "t_orderstat_pod");

        // assert
        assertTrue(result.get() instanceof HermesOrderUpdateParser,
            "IMWC bucket POD folder: Result is not an instance of HermesOrderUpdateParser");
    }

    @Test
    public void processS3Object_imwcBucketNameAndReturnsPrefix_returnHermesOrderReturnParser() {
        // act
        Optional result = archiveParserFactory.getParser(IMWC_BUCKET_NAME, "t_returnsum");

        // assert
        assertTrue(result.get() instanceof HermesOrderReturnParser,
            "IMWC bucket Return folder: Result is not an instance of HermesOrderReturnParser");
    }

    @Test
    public void processS3Object_imwcBucketWrongPrefix_returnEmpty() {
        // act
        Optional result = archiveParserFactory.getParser(IMWC_BUCKET_NAME, "wrong");

        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void processS3Object_hwcBucketNameAndOrderExportFolder_returnHermesOrderImportParser() {
        // act
        Optional result = archiveParserFactory.getParser(HWC_BUCKET_NAME, "export/");

        // assert
        assertTrue(result.get() instanceof HermesOrderExportParser,
            "HWC bucket export folder: Result is not an instance of HermesOrderExportParser");
    }

    @Test
    public void processS3Object_imwcBucketNameAndOrderExportFolder_returnHermesOrderImportParser() {
        // act
        Optional result = archiveParserFactory.getParser(IMWC_BUCKET_NAME, "Order_Import_40_");

        // assert
        assertTrue(result.get() instanceof HermesOrderExportParser,
            "IMWC bucket export folder: Result is not an instance of HermesOrderExportParser");
    }

    @Test
    public void processS3Object_omsBucketNameAndSfccOrderStatusFolder_returnSfccOrderStatusParser() {
        // act
        Optional result = archiveParserFactory.getParser(OMS_BUCKET_NAME, "sfcc-order-status/");

        // assert
        assertTrue(result.get() instanceof SfccOrderStatusExportParser,
            "OMS bucket sfcc order status folder: Result is not an instance of SfccOrderStatusExportParser");
    }

    @Test
    public void processS3Object_omsBucketNameAndTradebyteOrderStatusFolder_returnTradebyteOrderStatusParser() {
        // act
        Optional result = archiveParserFactory.getParser(OMS_BUCKET_NAME, "tb-order-status/");

        // assert
        assertTrue(result.get() instanceof TradebyteOrderStatusExportParser,
            "OMS bucket tb order status folder: Result is not an instance of TradebyteOrderStatusExportParser");
    }

    @Test
    public void processS3Object_osimcBucketNameAndReturnsPrefix_returnIngramMicroReturnsParser() {
        // act
        Optional result = archiveParserFactory.getParser(OSIMC_BUCKET_NAME, "returns");

        // assert
        assertTrue(result.get() instanceof IngramMicroOrderReturnParser,
            "OSIMC bucket Return folder: Result is not an instance of IngramMicroOrderReturnParser");
    }

    @Test
    public void processS3Object_plbcBucketNameAndReturnsPrefix_returnParcelLabPayloadReturnsParser() {
        // act
        Optional result = archiveParserFactory.getParser(PLBC_BUCKET_NAME, "payloads");

        // assert
        assertTrue(result.get() instanceof ParcelLabPayloadsParser,
            "PLBC bucket Payloads folder: Result is not an instance of ParcelLabPayloadsParser");
    }
}

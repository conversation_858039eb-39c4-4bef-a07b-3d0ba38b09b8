<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.bestseller</groupId>
    <artifactId>file-bundle-generator</artifactId>
    <version>1.0-SNAPSHOT</version>

    <name>File Bundle Generator</name>
    <description>BESTSELLER File Bundle Generator</description>
    <packaging>jar</packaging>

    <properties>
        <bse-checkstyle.version>1.0.19</bse-checkstyle.version>
        <java.version>21</java.version>
        <maven.checkstyle.plugin.version>3.3.1</maven.checkstyle.plugin.version>
        <maven.plugins.plugin.version>3.13.1</maven.plugins.plugin.version>
        <maven.surefire.plugin.version>3.3.0</maven.surefire.plugin.version>
        <maven.compiler.plugin.version>3.7.0</maven.compiler.plugin.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <aws-java-sdk-s3.version>1.12.261</aws-java-sdk-s3.version>
        <aws-lambda-java-core.version>1.2.0</aws-lambda-java-core.version>
        <aws-lambda-java-events.version>2.0.2</aws-lambda-java-events.version>

        <jsonschema2pojo-maven-plugin.version>0.4.37</jsonschema2pojo-maven-plugin.version>
        <commons-lang.version>2.6</commons-lang.version>
        <junit.jupiter.version>5.10.2</junit.jupiter.version>
        <mockito.version>5.11.0</mockito.version>
        <serenity.jbehave.version>1.24.0</serenity.jbehave.version>
        <serenity.version>1.2.4</serenity.version>
        <jbehave.version>4.1</jbehave.version>
        <awaitility.version>3.0.0</awaitility.version>
        <skipUnitTests>false</skipUnitTests>
        <byte-buddy.version>1.14.17</byte-buddy.version>
    </properties>

    <build>
        <plugins>
            <!-- package for the artifact that will be uploaded to aws lambda  -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <createDependencyReducedPom>false</createDependencyReducedPom>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- JSON Schema to POJO plugin -->
            <plugin>
                <groupId>org.jsonschema2pojo</groupId>
                <artifactId>jsonschema2pojo-maven-plugin</artifactId>
                <version>${jsonschema2pojo-maven-plugin.version}</version>
                <configuration>
                    <includeHashcodeAndEquals>false</includeHashcodeAndEquals>
                    <useBigDecimals>true</useBigDecimals>
                    <generateBuilders>true</generateBuilders>
                    <dateTimeType>java.time.ZonedDateTime</dateTimeType>
                    <dateType>java.time.ZonedDateTime</dateType>
                    <formatDates>true</formatDates>
                    <formatDateTimes>true</formatDateTimes>
                    <includeConstructors>true</includeConstructors>
                    <includeAdditionalProperties>false</includeAdditionalProperties>
                </configuration>
                <executions>
                    <execution>
                        <id>schema.json-generate</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <sourceDirectory>${project.basedir}/src/main/resources/schema/json/</sourceDirectory>
                            <targetPackage>fbg.generated.elasticsearch.model</targetPackage>
                            <outputDirectory>${project.basedir}/src/main/gensrc/</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- For running integration tests -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default</id>
                        <goals>
                            <goal>integration-test</goal>
                        </goals>
                        <configuration>
                            <includes>
                                <include>**/FunctionalTestSuite.java</include>
                            </includes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-checkstyle-plugin</artifactId>
                    <version>${maven.checkstyle.plugin.version}</version>
                    <dependencies>
                        <dependency>
                            <groupId>com.bestseller</groupId>
                            <artifactId>bse-checkstyle</artifactId>
                            <version>${bse-checkstyle.version}</version>
                        </dependency>
                    </dependencies>
                    <configuration>
                        <configLocation>com/bestseller/checkstyle.xml</configLocation>
                        <suppressionsLocation>${basedir}/checkstyle-suppressions.xml</suppressionsLocation>
                        <includeTestSourceDirectory>true</includeTestSourceDirectory>
                    </configuration>
                </plugin>
                <!-- For running unit tests -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven.surefire.plugin.version}</version>
                    <configuration>
                        <skipTests>${skipUnitTests}</skipTests>
                        <argLine>
                            --add-opens java.base/java.util=ALL-UNNAMED
                            --add-opens java.base/java.lang=ALL-UNNAMED
                            --add-opens java.base/java.util.stream=ALL-UNNAMED
                            --add-opens java.base/java.net=ALL-UNNAMED
                            --add-opens java.base/java.time=ALL-UNNAMED
                        </argLine>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <dependencies>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>${aws-java-sdk-s3.version}</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-lambda-java-core</artifactId>
            <version>${aws-lambda-java-core.version}</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-lambda-java-events</artifactId>
            <version>${aws-lambda-java-events.version}</version>
        </dependency>
        <!-- Logback dependencies for AWS Lambda -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.4.14</version>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>8.0</version>
        </dependency>
        <!-- SLF4J API (included with logback-classic but explicit for clarity) -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>2.0.9</version>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>${commons-lang.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>2.15.2</version>
        </dependency>
        <!-- JUnit 5 dependencies -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>${junit.jupiter.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.serenity-bdd</groupId>
            <artifactId>serenity-jbehave</artifactId>
            <version>${serenity.jbehave.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jbehave</groupId>
            <artifactId>jbehave-core</artifactId>
            <version>${jbehave.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jbehave</groupId>
            <artifactId>jbehave-spring</artifactId>
            <version>${jbehave.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.serenity-bdd</groupId>
            <artifactId>serenity-core</artifactId>
            <version>${serenity.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.serenity-bdd</groupId>
            <artifactId>serenity-junit</artifactId>
            <version>${serenity.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.serenity-bdd</groupId>
            <artifactId>serenity-spring</artifactId>
            <version>${serenity.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.awaitility</groupId>
            <artifactId>awaitility</artifactId>
            <version>${awaitility.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!-- Force consistent Jackson versions -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>2.15.2</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>2.15.2</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>2.15.2</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-cbor</artifactId>
                <version>2.15.2</version>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy</artifactId>
                <version>${byte-buddy.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy-agent</artifactId>
                <version>${byte-buddy.version}</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
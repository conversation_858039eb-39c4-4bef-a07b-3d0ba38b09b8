version: '3.0'

services:
  localstack:
    image: atlassianlabs/localstack
    ports:
      - "4572:4572"
      - "4574:4574"
    environment:
      - SERVICES=s3
      - DEBUG=${DEBUG- }
      - DEFAULT_REGION=eu-west-1
      - DATA_DIR=${DATA_DIR- }
      - DOCKER_HOST=unix:///var/run/docker.sock
    volumes:
      - "${TMP_DIR:-/tmp/localstack}:/tmp/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"
  provision:
    build: docker/awscli
    depends_on:
      - localstack
    volumes:
      - ./docker/provision/wait-for-it.sh:/project/wait-for-it.sh
      - ./docker/provision/provision.sh:/project/provision.sh
    environment:
      - AWS_ACCESS_KEY_ID=foobar
      - AWS_SECRET_ACCESS_KEY=foobar
      - AWS_DEFAULT_REGION=eu-west-1
    links:
      - localstack
    command: /bin/bash -c "/usr/bin/env chmod +x /project/wait-for-it.sh && /project/wait-for-it.sh localstack:4572 -t 20 -- /bin/bash provision.sh"
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:6.1.2
    environment:
      - discovery.type=single-node
    ports:
      - 9200:9200
      - 9330:9300

#!/usr/bin/env bash

AWS_S3="aws --endpoint-url http://localstack:4572 s3"

function create_bucket {
    local BUCKET_NAME=s3://$1
    ${AWS_S3} ls ${BUCKET_NAME} 2>&1 | grep -q 'NoSuchBucket' && ${AWS_S3} mb ${BUCKET_NAME}
}

create_bucket bse-oms-archive-dev.bseint.io
create_bucket bse-hwc-archive-dev.bseint.io
create_bucket bse-imwc-archive-dev.bseint.io
create_bucket bse-fbg-archive-dev.bseint.io
create_bucket bse-tbc-archive-dev.bseint.io
create_bucket bse-osimc-archive-dev.bseint.io


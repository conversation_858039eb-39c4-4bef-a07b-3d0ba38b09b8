# File bundle generator

The file bundle generator is a small application hooked to an AWS lambda which is used to index any file that is being archived.

[tclink]: https://teamcity.bs-ecommerce.com/overview.html
[Build Status][tclink]

# Contributing

Start by cloning the repository.

The general flow of work is simple:

- Create a branch out of master
- Get a green build (linting, unit tests, code coverage, etc)
- Get two code review approvals
- Get PO approval
- Ask for the ticket to be merged

# Documentation

Documentation is available in Confluence:
[TDD Order File Bundle Generation](https://bestseller.jira.com/wiki/spaces/BT/pages/247267426/OMS+Order+File+Bundle+Generation?src=jira)
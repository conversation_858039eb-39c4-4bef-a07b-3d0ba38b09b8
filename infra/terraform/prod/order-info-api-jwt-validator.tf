module "order_info_jwt_validator_lambda" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/lambda/aws"
  version = "~> 2.15.0"

  env     = local.env
  owner   = local.owner
  project = local.project
  vcs     = local.vcs

  function_name = local.order_info_api_jwt_validator_function_name
  memory_size   = 512
  timeout       = 600
  handler       = "oiaua.functions.UserAuthorizerHandler::handleRequest"
  runtime       = "java21"

  networking_enabled = "true"

  env_vars = local.env_vars_order_info_api_jwt
}

resource "aws_lambda_permission" "api_gateway_to_lambda_validate_token" {
  statement_id  = "AllowExecutionFromAPIGateway"
  action        = "lambda:InvokeFunction"
  function_name = module.order_info_jwt_validator_lambda.lambda_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${module.order_info_api_gateway.execution_arn}/*/${local.order_info_api_http_method}/${local.order_info_api_resource_path}"
}

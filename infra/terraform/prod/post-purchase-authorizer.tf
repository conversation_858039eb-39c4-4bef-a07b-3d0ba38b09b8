module "post-purchase-authorizer" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/lambda/aws"
  version = "~> 2.15.0"

  env     = local.env
  project = local.project
  vcs     = local.vcs
  owner   = local.owner

  # Lambda parameters
  function_name      = "post-purchase-authorizer"
  handler            = "authorizer.lambda_handler"
  runtime            = "python3.9"
  layers             = []
  memory_size        = 128
  timeout            = 30
  networking_enabled = false

  env_vars = {
    INITIATIVE_METHOD_ARN = "${module.post-purchase-api-gateway.execution_arn}/v1/POST/parcellab/events/webhook"

    # Users
    PARCELLAB_USERNAME = "parcellab-integration"
    PARCELLAB_SALT     = "Fr7m2rNr17U5gN9T+uI9wg=="
    PARCELLAB_KEY      = "e0UZHC5m1POHq6oue7fBwR2CH10pmQQz+YR633bCeOElrAqErrmvliOhzbEfUZjhpVNvfYOJRjiktugyzLVLDA=="

    USE_DATADOG_LOGGER = true
  }
}

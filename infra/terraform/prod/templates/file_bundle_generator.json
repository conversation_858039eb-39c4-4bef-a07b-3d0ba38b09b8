{"swagger": "2.0", "info": {"version": "${timestamp}", "title": "order-bundle-${env}"}, "basePath": "/${env}", "schemes": ["https"], "paths": {"/${resource_path}": {"get": {"parameters": [{"name": "orderBundle", "in": "path", "required": true, "type": "string"}], "responses": {"302": {"description": "302 response"}}, "security": [{"sigv4": []}], "x-amazon-apigateway-integration": {"httpMethod": "POST", "uri": "arn:aws:apigateway:${aws_region}:lambda:path/2015-03-31/functions/${lambda_order_bundle_arn}/invocations", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "contentHandling": "CONVERT_TO_TEXT", "type": "aws_proxy"}}}, "/${orange_resource_path}": {"get": {"parameters": [{"name": "orange", "in": "path", "required": true, "type": "string"}], "responses": {"302": {"description": "302 response"}}, "security": [{"sigv4": []}], "x-amazon-apigateway-integration": {"httpMethod": "POST", "uri": "arn:aws:apigateway:${aws_region}:lambda:path/2015-03-31/functions/${lambda_orange_arn}/invocations", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "contentHandling": "CONVERT_TO_TEXT", "type": "aws_proxy"}}}}, "securityDefinitions": {"sigv4": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-amazon-apigateway-authtype": "awsSigv4"}}}
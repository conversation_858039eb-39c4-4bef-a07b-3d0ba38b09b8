{"swagger": "2.0", "info": {"description": "Ship From Store backend", "version": "0.0.1", "title": "SFS"}, "basePath": "/v1", "schemes": ["https"], "paths": {"/fulfillmenttools/events/webhook": {"post": {"security": [{"authorizer-function": []}], "x-amazon-apigateway-integration": {"type": "aws_proxy", "httpMethod": "POST", "uri": "${receive_fulfillmenttools_events_integration_uri}"}}}}, "securityDefinitions": {"authorizer-function": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-amazon-apigateway-authtype": "custom", "x-amazon-apigateway-authorizer": {"type": "token", "identitySource": "method.request.header.Authorization", "authorizerUri": "${authorizer_uri}", "authorizerCredentials": "${authorizer_role_arn}", "authorizerResultTtlInSeconds": 300}}}}
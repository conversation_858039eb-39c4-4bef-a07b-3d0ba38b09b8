module "sfs-authorizer" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/lambda/aws"
  version = "~> 2.15.0"

  env     = local.env
  project = local.project
  vcs     = local.vcs
  owner   = local.owner

  # Lambda parameters
  function_name      = "sfs-authorizer"
  handler            = "authorizer.lambda_handler"
  runtime            = "python3.9"
  layers             = []
  memory_size        = 128
  timeout            = 30
  networking_enabled = false

  env_vars = {
    INITIATIVE_METHOD_ARN = "${module.sfs-api-gateway.execution_arn}/v1/POST/fulfillmenttools/events/webhook"

    # Users
    FULFILLMENTTOOLS_USERNAME = "fulfillmenttools"
    FULFILLMENTTOOLS_SALT     = "RlLS3lbDr/z83IlBcqbA1g=="
    FULFILLMENTTOOLS_KEY      = "OjCPp9TZOr9gY2q3and8zRO95ombjaHlkUcf2cLtt7Q15X0Me96KQNexZMLXq0Fnz8VxxnbyguJ4mRRd7leqMA=="

    USE_DATADOG_LOGGER = true
  }
}

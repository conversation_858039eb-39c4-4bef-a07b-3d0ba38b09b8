module "order_info_api_jwt_generator" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/lambda/aws"
  version = "~> 2.15.0"

  env     = local.env
  owner   = local.owner
  project = local.project
  vcs     = local.vcs

  function_name      = local.order_info_api_jwt_generator_function_name
  memory_size        = 512
  timeout            = 600
  handler            = "oiaua.functions.UserAuthorizerHandler::handleRequest"
  runtime            = "java21"
  networking_enabled = "true"

  env_vars = local.env_vars_order_info_api_jwt
}

resource "aws_lambda_permission" "api_gateway_to_lambda_generate_token" {
  statement_id  = "AllowExecutionFromAPIGateway"
  action        = "lambda:InvokeFunction"
  function_name = module.order_info_api_jwt_generator.lambda_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${module.order_info_api_gateway.execution_arn}/*/${local.order_info_api_jwt_generator_api_http_method}/${local.order_info_api_jwt_generator_api_resource_path}"
}

resource "aws_api_gateway_api_key" "OrderInfoJwtGeneratorApiKey" {
  name = "${local.order_info_api_jwt_generator_function_name}-${local.env}-key"
}

resource "aws_api_gateway_usage_plan" "jwtGenerator" {
  name = local.order_info_api_jwt_generator_function_name

  api_stages {
    api_id = module.order_info_api_gateway.ids["id"]
    stage  = local.env
  }
}
resource "aws_api_gateway_usage_plan_key" "jwtGenerator" {
  key_id        = aws_api_gateway_api_key.OrderInfoJwtGeneratorApiKey.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.jwtGenerator.id
}

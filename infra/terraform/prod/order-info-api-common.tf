locals {
  order_info_api_project       = "order-info-api"
  order_info_api_url           = "orders"
  order_info_api_http_method   = "GET"
  order_info_api_resource_path = "{orderId}"

  order_info_api_user_authorizer_path            = "order-info-api-user-authorizer"
  order_info_api_jwt_generator_function_name     = "order-info-jwt-generator"
  order_info_api_jwt_validator_function_name     = "order-info-jwt-validator"
  order_info_api_jwt_generator_api_http_method   = "POST"
  order_info_api_jwt_generator_api_resource_path = "login"

  zendesk = "zendesk"

  env_vars_order_info_api_jwt = {
    "ISSUER" : "bestseller-${local.env}",
    "SUBJECT" : "${local.order_info_api_project}-user-${local.zendesk}-${local.env}",
    "AUDIENCE" : "${local.zendesk}-${local.env}",
    "TOKEN_VALID_TIME_IN_SECONDS" : "300",
    "JWT_PRIVATE_KEY_SECRET_NAME" : module.jwt_private_key.secret_name,
    "JWT_PUBLIC_KEY" : "-----BEGIN PUBLIC KEY-----MIIBojANBgkqhkiG9w0BAQEFAAOCAY8AMIIBigKCAYEAsrg1n5FUs8rpKk3VmxcKo9QSwV4ccw+zFkidkM5S5wa3wyD/XwINyam2PeMBnrF4JCLFEBdsGoqQWtrnCRuTx8fXc54NuAcdlAbi9rOGQ/jOaQaFiTB0fWyDMihDJ5Km3qxXqgm545AWTckDTMZoXcs+uEIgbpALpuotpxq6W6wbRB7JhwMM2srgNGA+n4MliFCpfER+pu6dX7bZphizTgxR+B2KSPgBM6iW6rIbslVgsKtm3t1U/XbVfPMoSOYdDrNlrmpACSBt9ndTZr1rD9R475DaZgjmJM0Atk2bf591NxjpvUvdRz53pbwGyMk8cQNYJZkatZQLZwgp3mF7YZelirUxNZVAOMng6rxm36874m+kxxOhVlU+djh/j44pbKmooSFSQQBaX1n7XN8IlYyz32HTpfj8Ptf+lV8uzOI+/dGMzQzo2+mzT0iRnoIMF4cXtRTuFLPsYQTrLESzm3cC8/k7arP7E6cOoeKrzLqo/7vpk9FB2MWrPlG5nCD5AgMBAAE=-----END PUBLIC KEY-----"
  }
}
module "sfs-authorizer-invocation-role" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/iam-role/aws"
  version = "~> 0.0.0"

  env     = local.env
  owner   = local.owner
  project = local.project
  vcs     = local.vcs

  role_name = "sfs-authorizer-invocation-role"

  assumed_by_principals = [
    {
      type       = "Service",
      identifier = "apigateway.amazonaws.com"
    }
  ]

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid      = "AllowInvokeAuthorizerFunction"
        Effect   = "Allow"
        Action   = "lambda:InvokeFunction"
        Resource = module.sfs-authorizer.lambda_arn
      },
      {
        Sid    = "AllowAccessLoggingAndExecutionLogging"
        Effect = "Allow"
        Action = [
          # TODO reuse existing policy AmazonAPIGatewayPushToCloudWatchLogs
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:DescribeLogGroups",
          "logs:DescribeLogStreams",
          "logs:PutLogEvents",
          "logs:GetLogEvents",
          "logs:FilterLogEvents"
        ]
        Resource = "*"
      }
    ]
  })
}

module "sfs-api-gateway" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/api-gateway/aws"
  version = "~> 1.1.0"

  project = local.project
  env     = local.env
  owner   = local.owner
  vcs     = local.vcs

  name                  = "sfs"
  stage_name            = "v1"
  domain_name           = "fulfilment-sfs.${local.env}.bseint.io"
  domain_name_acm_arn   = data.aws_acm_certificate.virginia_cert.arn
  domain_name_zone_id   = data.aws_route53_zone.zone.zone_id
  authorizer_lambda_arn = "arn:aws:lambda:${local.aws_region}:${local.aws_account_id}:function:${local.project}-sfs-authorizer-${local.env}"
  description           = "Ship From Store backend"

  body = jsonencode(jsondecode(templatefile("${path.module}/templates/sfs-swagger.tpl", {
    authorizer_role_arn = "arn:aws:iam::${local.aws_account_id}:role/${local.env}_${local.project}_sfs-authorizer-invocation-role"
    authorizer_uri      = "arn:aws:apigateway:${local.aws_region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${local.aws_region}:${local.aws_account_id}:function:${local.project}-sfs-authorizer-${local.env}/invocations"

    receive_fulfillmenttools_events_integration_uri = "arn:aws:apigateway:${local.aws_region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${local.aws_region}:${local.aws_account_id}:function:${local.project}-receive-fft-events-${local.env}/invocations"
  })))

  tags = tomap(
    {
      env          = local.env,
      project_name = local.project_name,
      project      = local.project,
      owner        = local.owner,
      vcs          = local.vcs
    }
  )
}

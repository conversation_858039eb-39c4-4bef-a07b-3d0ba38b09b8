module "jwt_private_key" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/secretsmanager-secret/aws"
  version = "~> 1.0.0"

  owner   = local.owner
  project = local.project
  env     = local.env
  vcs     = local.vcs

  secret_name        = "JWT_PRIVATE_KEY"
  secret_description = "ORDER INFO JWT PRIVATE KEY"
  secret_string      = "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"
}

module "okta_api_secret_token" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/secretsmanager-secret/aws"
  version = "~> 1.0.0"

  owner   = local.owner
  project = local.project
  env     = local.env
  vcs     = local.vcs

  secret_name        = "OKTA_API_SECRET_TOKEN"
  secret_description = "OKTA AUTHURIZATION SERVER SECRET TOKEN"
  secret_string      = "AQECAHid/iMdnrz7tlfTYN7Qxf0HqciGChiX2z14xGo/X6u/qgAAAKIwgZ8GCSqGSIb3DQEHBqCBkTCBjgIBADCBiAYJKoZIhvcNAQcBMB4GCWCGSAFlAwQBLjARBAy/5j2WrOJCOl4jX24CARCAWysI/Y+mldBDc8WXFLY/EeSmRSIMOvy2vtg4FnGdmjM1M9OYrPkKFEpovqjTjoP4X/ytcGdYlVy23FBsLtJj05yvujwvW1zIJG40/MF+LI9btMUiDvUC4XVvH1Q="
}


data "aws_secretsmanager_secret_version" "jwt_private_key_unencrypted" {
  secret_id  = module.jwt_private_key.secret_arn
  depends_on = [module.jwt_private_key]
}
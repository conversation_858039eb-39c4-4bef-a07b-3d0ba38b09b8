module "post-purchase-authorizer" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/lambda/aws"
  version = "~> 2.15.0"

  env     = local.env
  project = local.project
  vcs     = local.vcs
  owner   = local.owner

  # Lambda parameters
  function_name      = "post-purchase-authorizer"
  handler            = "authorizer.lambda_handler"
  runtime            = "python3.9"
  layers             = []
  memory_size        = 128
  timeout            = 30
  networking_enabled = false

  env_vars = {
    INITIATIVE_METHOD_ARN = "${module.post-purchase-api-gateway.execution_arn}/v1/POST/parcellab/events/webhook"

    # Users
    PARCELLAB_USERNAME = "parcellab-integration"
    PARCELLAB_SALT     = "+jHbauKimR2x6rXcKOFZ3w=="
    PARCELLAB_KEY      = "A/rZTlpLkaFinMcPn7xn2MSIWgYyujHV6PSVpO6GGOqBwYmbCCKjd5j+eg1lX7Q1nz+FaM8jsG4880l5ab7IJQ=="

    USE_DATADOG_LOGGER = true
  }
}

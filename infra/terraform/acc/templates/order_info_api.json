{"swagger": "2.0", "info": {"version": "${timestamp}", "title": "order-info-api-${env}"}, "basePath": "", "schemes": ["https"], "paths": {"/${jwt_generator_api_resource_path}": {"post": {"responses": {"302": {"description": "302 response"}}, "security": [{"api_key": []}], "x-amazon-apigateway-integration": {"httpMethod": "POST", "uri": "arn:aws:apigateway:${aws_region}:lambda:path/2015-03-31/functions/${jwt_generator_lambda_arn}/invocations", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "contentHandling": "CONVERT_TO_TEXT", "type": "aws_proxy"}}}, "/${api_resource_path}": {"get": {"responses": {"302": {"description": "302 response"}}, "security": [{"authorizer-function": []}], "x-amazon-apigateway-integration": {"httpMethod": "POST", "uri": "arn:aws:apigateway:${aws_region}:lambda:path/2015-03-31/functions/${api_lambda_arn}/invocations", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "contentHandling": "CONVERT_TO_TEXT", "type": "aws_proxy"}}}}, "securityDefinitions": {"authorizer-function": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "x-amazon-apigateway-authtype": "custom", "x-amazon-apigateway-authorizer": {"type": "request", "identitySource": "method.request.header.Authorization", "authorizerUri": "${authorizer_uri}", "authorizerCredentials": "${authorizer_role_arn}", "authorizerResultTtlInSeconds": 300}}, "api_key": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-api-key", "in": "header"}}}
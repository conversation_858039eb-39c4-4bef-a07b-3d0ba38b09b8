module "sfs-authorizer" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/lambda/aws"
  version = "~> 2.15.0"

  env     = local.env
  project = local.project
  vcs     = local.vcs
  owner   = local.owner

  # Lambda parameters
  function_name      = "sfs-authorizer"
  handler            = "authorizer.lambda_handler"
  runtime            = "python3.9"
  layers             = []
  memory_size        = 128
  timeout            = 30
  networking_enabled = false

  env_vars = {
    INITIATIVE_METHOD_ARN = "${module.sfs-api-gateway.execution_arn}/v1/POST/fulfillmenttools/events/webhook"

    # Users
    FULFILLMENTTOOLS_USERNAME = "fulfillmenttools"
    FULFILLMENTTOOLS_SALT     = "k8F9XrjfEU+DyW6KQygKRA=="
    FULFILLMENTTOOLS_KEY      = "XlZcGX8eopO17Zu97IuA9jz9Suj0MhobsBuXg5GBJrSzKJuaxzdKLRN1mZPF7BVy2ePvdmFbc5IXQyNPB7Jd3A=="

    USE_DATADOG_LOGGER = true
  }
}

module "order_info_api_gateway" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/api-gateway/aws"
  version = "~> 1.1.0"

  project = local.order_info_api_project
  env     = local.env
  owner   = local.owner
  vcs     = local.vcs

  stage_name           = local.env
  domain_name          = "${local.order_info_api_url}.${local.env}.bseint.io"
  domain_name_acm_arn  = data.aws_acm_certificate.virginia_cert.arn
  domain_name_zone_id  = data.aws_route53_zone.zone.zone_id
  xray_tracing_enabled = true
  description          = "Api gateway for Order Info API"

  body = templatefile("templates/order_info_api.json", {
    aws_region                      = local.aws_region
    env                             = local.env
    timestamp                       = timestamp()
    jwt_generator_api_resource_path = local.order_info_api_jwt_generator_api_resource_path
    api_resource_path               = local.order_info_api_resource_path
    jwt_generator_lambda_arn        = module.order_info_api_jwt_generator.lambda_arn
    api_lambda_arn                  = module.order_info_api.lambda_arn
    authorizer_role_arn             = "arn:aws:iam::${local.aws_account_id}:role/${local.env}_${local.order_info_api_project}_invocation_role"
    authorizer_uri                  = "arn:aws:apigateway:${local.aws_region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${local.aws_region}:${local.aws_account_id}:function:${local.project}-${local.order_info_api_jwt_validator_function_name}-${local.env}/invocations"
  })

  tags = tomap(
    {
      env          = local.env,
      project_name = local.order_info_api_project,
      project      = local.project,
      owner        = local.owner,
      vcs          = local.vcs
    }
  )
}
# Configure remote state
terraform {
  backend "remote" {
    hostname     = "tfe.mng.bseint.io"
    organization = "bestseller-ecom"

    workspaces {
      name = "logistic_lambdas-acc"
    }
  }
}

// Define global variables
locals {
  // Setup - DO NOT CHANGE!
  aws_account_id = "************"
  aws_region     = "eu-west-1"

  //Project
  env                 = "acc"
  project_information = module.metadata.projects["logistic_lambdas"]["metadata"]
  project_name        = local.project_information["project_name"]
  project             = local.project_information["project"]
  owner               = local.project_information["owner"]
  vcs                 = local.project_information["github_vcs"]
  cost_estimate       = "50.0"

  bseint_domain = "bseint.io"
}

module "metadata" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/metadata/bestseller"
  version = "~> 2.0.0"
}

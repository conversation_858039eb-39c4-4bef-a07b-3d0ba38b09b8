module "receive-parcellab-events" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/lambda/aws"
  version = "~> 2.15.0"

  env     = local.env
  project = local.project
  vcs     = local.vcs
  owner   = local.owner

  # Lambda parameters
  function_name      = "receive-parcellab-events"
  handler            = "lambda_function.lambda_handler"
  runtime            = "python3.9"
  layers             = []
  memory_size        = 128
  timeout            = 30
  networking_enabled = true

  env_vars = {
    KAFKA_BROKER_LIST  = "kafka-ecom-temporal.acc.bseint.io:32241"
    USE_DATADOG_LOGGER = true
    KAFKA_TOPIC        = "PostPurchaseEventReceived"
  }

}

resource "aws_lambda_permission" "receive-parcellab-events" {
  statement_id  = "AllowExecutionFromApiGateway"
  action        = "lambda:InvokeFunction"
  function_name = module.receive-parcellab-events.lambda_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${module.post-purchase-api-gateway.execution_arn}/v1/POST/parcellab/events/webhook"
}


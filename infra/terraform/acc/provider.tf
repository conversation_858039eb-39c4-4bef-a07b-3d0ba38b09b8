# enables referencing resource in us-east-1
provider "aws" {
  alias  = "virginia_provider"
  region = "us-east-1"

  assume_role {
    session_name = "terraform"
    role_arn     = "arn:aws:iam::${local.aws_account_id}:role/terraform-admin-role"
  }
}

provider "aws" {
  region              = local.aws_region
  allowed_account_ids = [local.aws_account_id]
  assume_role {
    role_arn = "arn:aws:iam::${local.aws_account_id}:role/terraform-admin-role"
  }
}
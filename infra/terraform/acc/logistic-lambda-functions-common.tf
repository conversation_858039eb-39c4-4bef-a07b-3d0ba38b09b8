locals {
  vcs_root_id                = "Logistics_LogisticLambdas_LogisticsLambdaFunctions"
  elastic_search_domain_name = "bse-file-index-${local.env}"
  s3_bucket_domain_name      = "bseint.io"
  default_tags = {
    env         = local.env
    project     = local.project
    owner       = local.owner
    vcs         = local.vcs
    Terraformed = "true"
    Ansible     = "false"
  }
}

data "aws_security_group" "elasticsearch_default" {
  name = "secgp-elasticsearch-default"
}

data "aws_security_group" "file_index_db" {
  name = "secgp-file-index-db"
}

data "aws_security_group" "file_index_app" {
  name = "secgp-file-index-app"
}

data "aws_security_group" "lambdas" {
  name = "secgp-lambda-default"
}

module "data_vpc_base" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/vpc-data/aws"
  version = "~> 1.1.0"

  env = local.env
}

resource "aws_security_group" "secgp_inbound_lambdas" {
  name        = "secgp-inbound-lambdas"
  description = "Allows incoming traffic from lambdas"
  vpc_id      = module.data_vpc_base.vpc_base_id

  tags = merge(
    {
      "Name" = "secgp-inbound-lambdas"
    },
    local.default_tags,
  )

  ingress {
    from_port       = 0
    to_port         = 65535
    protocol        = "tcp"
    security_groups = [data.aws_security_group.lambdas.id]
  }
}

data "aws_iam_policy_document" "source" {
  statement {
    actions = ["es:*"]
    effect  = "Allow"
    principals {
      type        = "AWS"
      identifiers = ["*"]
    }
    resources = ["arn:aws:es:${local.aws_region}:${local.aws_account_id}:domain/${local.elastic_search_domain_name}/*"]
  }
}
module "order_bundle_api" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/api-gateway/aws"
  version = "1.1.1"

  project = local.project_path
  env     = local.env
  owner   = local.owner
  vcs     = local.vcs

  stage_name = local.env

  description = "Api gateway for orderbundle"
  body = templatefile("templates/file_bundle_generator.json", {
    aws_region              = local.aws_region
    env                     = local.env
    resource_path           = local.api_gateway_resource_path
    orange_resource_path    = local.api_gateway_orange_resource_path
    timestamp               = timestamp()
    account_id              = local.aws_account_id
    lambda_order_bundle_arn = module.file_bundle_generator_download_lambda.lambda_arn
    lambda_orange_arn       = module.file_orange_generator_download_lambda.lambda_arn
  })

  tags = tomap(
    {
      env          = local.env,
      project_name = local.project_path,
      project      = local.project,
      owner        = local.owner,
      vcs          = local.vcs
    }
  )
}
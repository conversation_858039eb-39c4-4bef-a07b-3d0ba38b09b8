locals {
  order_info_api_project       = "order-info-api"
  order_info_api_url           = "orders"
  order_info_api_http_method   = "GET"
  order_info_api_resource_path = "{orderId}"

  order_info_api_user_authorizer_path            = "order-info-api-user-authorizer"
  order_info_api_jwt_generator_function_name     = "order-info-jwt-generator"
  order_info_api_jwt_validator_function_name     = "order-info-jwt-validator"
  order_info_api_jwt_generator_api_http_method   = "POST"
  order_info_api_jwt_generator_api_resource_path = "login"

  zendesk = "zendesk"

  env_vars_order_info_api_jwt = {
    "ISSUER" : "bestseller-${local.env}",
    "SUBJECT" : "${local.order_info_api_project}-user-${local.zendesk}-${local.env}",
    "AUDIENCE" : "${local.zendesk}-${local.env}",
    "TOKEN_VALID_TIME_IN_SECONDS" : "300",
    "JWT_PRIVATE_KEY_SECRET_NAME" : module.jwt_private_key.secret_name,
    "JWT_PUBLIC_KEY" : "-----BEGIN PUBLIC KEY-----MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtgW4V8p875LBXzSo4oVy1nTcHBTVQl4CCMZoZA0IHP56Sue/LqUq4Jg5am3SqNPKgUtEitMCVE0RNvoTlM6deC10DWpan1h3UhpGmIF6HlJPftMiFdRJ7tIxO81XL9ovW12f6yi/Fn/qf6AhG03MExLug8b65dYofvmn4wdAvT54lSMVEoNWZm08FILgI2zwLiwghy1EIKHoTgiuuLUPuEf52koxOrb4OEB3vzrfj7x1iZyMj5xyoydXtJFsEo0M11wJmMYcpcmPJhGqH7ioDw82MebU+D3TOWJ4yJipH96ervLsWCTbzLe6IMhxsOrp9XQJHu6Cq0RyD5UJkE6frwIDAQAB-----END PUBLIC KEY-----"
  }
}
locals {
  api_gateway_orange_http_method   = "GET"
  api_gateway_orange_resource_path = "orange/{orderId}"
}

module "file_orange_generator_download_lambda" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/lambda/aws"
  version = "~> 2.15.0"

  env                = local.env
  owner              = local.owner
  project            = local.project
  vcs                = local.vcs
  function_name      = "file-orange-download"
  memory_size        = 512
  timeout            = 600
  handler            = "fbg.functions.BundleGenerationHandler::handleRequest"
  runtime            = "java21"
  networking_enabled = "true"

  iam_task_custom_policy_json = data.aws_iam_policy_document.fbg_policy_document.json
  env_vars = {
    "S3_ARCHIVE_ORANGES_BUCKET" = "bse-imwc-${local.env}.${local.s3_bucket_domain_name}"
  }
}

resource "aws_lambda_permission" "apigw_orange_lambda" {
  statement_id  = "AllowExecutionFromAPIGateway"
  action        = "lambda:InvokeFunction"
  function_name = module.file_orange_generator_download_lambda.lambda_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${module.order_bundle_api.execution_arn}/*/${local.api_gateway_orange_http_method}/${local.api_gateway_orange_resource_path}"
}

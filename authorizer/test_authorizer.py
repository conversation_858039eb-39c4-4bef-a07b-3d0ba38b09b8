from unittest import TestCase
from base64 import b64encode
from unittest.mock import patch
import os

import authorizer


class TestAuthorizer(TestCase):
    username = "my-user"
    password = "qwert"

    def setUp(self, username=username, password=password):
        self.maxDiff = None

        self.method_arn = "arn:aws:execute-api:ru-central-1:142857142857:Z1ON0101/prod/POST/iso/order/{brand}"
        self.method_mask = "arn:aws:execute-api:ru-central-1:142857142857:Z1ON0101/prod/POST/iso/order/*"

        os.environ["INITIATIVE_METHOD_ARN"] = self.method_arn

        salt = b"NaCl"
        os.environ["WHOEVER_SALT"] = b64encode(salt).decode("UTF-8")
        key = authorizer.hash_password(password, salt)
        os.environ["WHOEVER_KEY"] = b64encode(key).decode("UTF-8")
        os.environ["WHOEVER_USERNAME"] = username

    def _make_event(self, username, password):
        creds = b64encode(":".join([username, password]).encode("UTF-8")).decode(
            "UTF-8"
        )
        return {
            "type": "TOKEN",
            "methodArn": self.method_arn,
            "resource": "/iso-orders",
            "path": "/iso-orders",
            "httpMethod": "POST",
            "authorizationToken": f" Basic {creds} ",  # note extra spaces
        }

    def test_lambda_handler(self, username=username, password=password):
        # arrange
        event = self._make_event(username, password)
        context = None

        # act
        policy = authorizer.lambda_handler(event, context)

        # assert
        self.assertEqual(
            policy,
            {
                "principalId": username,
                "policyDocument": {
                    "Version": "2012-10-17",
                    "Statement": [
                        {
                            "Sid": "ApiAuthorizationVerdict0",
                            "Action": "execute-api:Invoke",
                            "Effect": "Allow",
                            "Resource": self.method_mask,
                        }
                    ],
                },
            },
        )

    def test_lambda_handler_wrong_password(self, username=username):
        # arrange
        event = self._make_event(username, "letmein")
        context = None

        # act
        policy = authorizer.lambda_handler(event, context)

        # assert
        effects = {
            statement["Effect"] for statement in policy["policyDocument"]["Statement"]
        }
        self.assertEqual(effects, set(["Deny"]))

#!/bin/bash -eu

# Set default artifact name
: "${ARTIFACT_NAME:=$(basename "$(dirname "$0")")/artifact.zip}"

# Resolve absolute path
ARTIFACT_NAME="$(readlink -m "$ARTIFACT_NAME")"

BUILD_DIR=build

# Change to the project sub-directory
pushd "$(dirname "$0")"

venv() {
  echo Creating virtual environment
  # work within a virtual environment
  # because we may not have enough permissions in the default $PYTHONUSERBASE
  # for example, if we're running in a Docker container and we're not root
  python -m venv ./venv
  . ./venv/bin/activate

  pip --version
  python --version
}

deps() {
  echo Installing dependencies
  mkdir -p "$BUILD_DIR"
  pip install -t "$BUILD_DIR" -r requirements.txt
}

package() {
  echo Packaging artifact "$ARTIFACT_NAME"
  python -m zipfile -c "$ARTIFACT_NAME" "$BUILD_DIR"/* authorizer.py
}

test() {
  # Add build directory to the module search path
  export PYTHONPATH="$BUILD_DIR:${PYTHONPATH-}"

  echo Running doc tests
  python -m doctest *.py

  echo Running unit tests
  python -m unittest
}

venv
deps
test
package

"""
Allows or denies API requests
"""
from hashlib import scrypt
from base64 import b64decode, b64encode
import os
import re
import logging
import json_log_formatter

SALT_RECOMMENDED_BITS = 128

log = logging.getLogger(__name__)

# to update the formatter of the logger that used by AWS lambda...
# Without this(adding new log handler) the log message is duplicated in DD.
root_logger = logging.getLogger()
if bool(os.getenv("USE_DATADOG_LOGGER")) and len(root_logger.handlers) > 0:
    root_logger.handlers[0].setFormatter(json_log_formatter.VerboseJSONFormatter())

log.setLevel(logging.INFO)


def load_user_creds(environ):
    return {
        username: (
            b64decode(environ[varname.replace("_USERNAME", "_SALT")]),
            b64decode(environ[varname.replace("_USERNAME", "_KEY")]),
        )
        for varname, username in environ.items()
        if varname.endswith("_USERNAME")
    }


def lambda_handler(event, context):
    """
    Entry point function
    :param event: Event data passed in by AWS
    :param context: Event context
    """
    # read environment
    method_arns = re.sub(r"{\w+}", "*", os.environ["INITIATIVE_METHOD_ARN"]).split()
    user_creds = load_user_creds(os.environ)

    # read request
    auth = http_authorization(event)
    username, password = parse_credentials(auth)

    # validate credentials
    try:
        salt, key = user_creds[username]
        computed_key = hash_password(password, salt)
        authorized = computed_key == key
        log.debug(
            "User %s:%s", "authorized" if authorized else "not authorized", username
        )
    except KeyError:
        log.warning("User not found: %s", username)
        authorized = False

    # create policy
    # policy must be complete because it will be applied to multiple requests
    policy = {
        "principalId": username,
        "policyDocument": {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Sid": f"ApiAuthorizationVerdict{i}",
                    "Action": "execute-api:Invoke",
                    "Effect": "Allow" if authorized else "Deny",
                    "Resource": method_arn,
                }
                for i, method_arn in enumerate(method_arns)
            ],
        },
    }
    log.debug("Generated policy: %s", policy)
    return policy


def http_authorization(event):
    """
    Extract the `Basic FooBar==` value from the token or header
    :param event: AWS event object
    """
    if event["type"] == "TOKEN":
        authorization = event["authorizationToken"]

        # remove sensitive fields
        del event["authorizationToken"]
    elif event["type"] == "REQUEST":
        authorization = event["headers"]["Authorization"]

        # remove sensitive fields
        del event["headers"]["Authorization"]
        del event["multiValueHeaders"]["Authorization"]
    else:
        raise AssertionError(f"Unexpected event type: {event['type']}")

    log.debug("Event (redacted):%s", event)
    return authorization


def parse_credentials(http_auth):
    """
    Extract username and password
    :param http_auth: HTTP authorization string, like `Basic FooBar==`
    """
    *scheme, creds = http_auth.split()
    username, password = b64decode(creds).decode("UTF-8").split(":", maxsplit=1)
    return username, password


def hash_password(password, salt):
    work_factor = 2**14
    block_size = 8
    parallelization = 1  # disallow trading CPU for memory
    log.debug(
        f"Memory required for scrypt: {work_factor * (2 * block_size) * 64} bytes"
    )
    return scrypt(
        password.encode("UTF-8"),
        salt=salt,
        n=work_factor,
        r=block_size,
        p=parallelization,
    )


if __name__ == "__main__":
    from os import urandom
    from getpass import getpass

    password = getpass(f"New password [Empty: use current]:")
    if password:
        salt = urandom(SALT_RECOMMENDED_BITS // 8)
        key = hash_password(password, salt)
        print("Set new password via environment variables:")
        print("WHATEVER_USERNAME", "<EMAIL>", sep="=")
        print("WHATEVER_SALT=\"" + b64encode(salt).decode("UTF-8") + "\"")
        print("WHATEVER_KEY=\"" + b64encode(key).decode("UTF-8") + "\"")
    else:
        user_creds = load_user_creds(os.environ)
        for user, (salt, key) in user_creds.items():
            print(f"Loaded user {user} from environment variables")
            break
        else:
            print("No users loaded from environment variables")
            exit(1)

    password = getpass(f"Verify password:")
    if key == hash_password(password, salt):
        print("Password matches")
    else:
        print("Password doesn't match")
        exit(1)

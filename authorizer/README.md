# Authorizer

Function allows and denies access through REST API.


## Prerequisites

Python 3.9

## Setting up local environment

1. Create virtual environment

        python -m venv ./venv

2. Activate virtual environment in the current shell

        . ./venv/bin/activate

3. Install dependencies

        pip install -r requirements.txt

## Running tests

1. Run unit tests

        python -m unittest

2. Reformat the code

        pip install black
        python -m black *.py


## Encrypt password

Create new or verify current password

    python -m authorizer


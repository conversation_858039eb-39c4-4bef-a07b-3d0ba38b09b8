exclude: ".md$"
repos:
-   repo: local
    hooks:
    -   id: clean-terraform-init
        name: Clean Terraform Cache
        entry: bash -c "find . -name '.terraform*' -print0 | xargs -0 rm -r || exit 0"
        language: system
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v3.2.0
    hooks:
    -   id: trailing-whitespace
        files: ".tf$"
    -   id: end-of-file-fixer
        files: ".tf$"
    -   id: check-yaml
        files: ".tf$"
    -   id: check-added-large-files
        files: ".tf$"
-  repo: https://github.com/antonbabenko/pre-commit-terraform
   rev: v1.62.3
   hooks:
   -   id: terraform_fmt
       files: ".tf$"
   -   id: terraform_validate
       files: ".tf$"

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.bestseller</groupId>
    <artifactId>order-info-api-user-authorizer</artifactId>
    <version>1.0-SNAPSHOT</version>

    <name>Order Info API User Authorizer</name>
    <description>BESTSELLER Order Info API User Authorizer</description>
    <packaging>jar</packaging>

    <properties>
        <java.version>21</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <aws-java-sdk.version>1.12.778</aws-java-sdk.version>
        <aws-lambda-java-core.version>1.3.0</aws-lambda-java-core.version>
        <aws-lambda-java-events.version>3.14.0</aws-lambda-java-events.version>
        <logback.version>1.5.15</logback.version>
        <logstash-logback-encoder.version>8.0</logstash-logback-encoder.version>
        <nimbus-jwt.version>9.47</nimbus-jwt.version>
        <httpcore.version>4.4.16</httpcore.version>
        <jackson.version>2.15.2</jackson.version>
        <junit.version>5.11.4</junit.version>
        <mockito.version>5.18.0</mockito.version>
        <system-stubs.version>2.1.7</system-stubs.version>
        <bse-checkstyle.version>1.0.19</bse-checkstyle.version>

        <maven.compiler.plugin.version>3.13.0</maven.compiler.plugin.version>
        <maven.shade.plugin.version>3.6.0</maven.shade.plugin.version>
        <maven.checkstyle.plugin.version>3.5.0</maven.checkstyle.plugin.version>
        <maven.surefire.plugin.version>3.5.3</maven.surefire.plugin.version>

        <skipUnitTests>false</skipUnitTests>
    </properties>

    <build>
        <plugins>
            <!-- package for the artifact that will be uploaded to aws lambda  -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>${maven.shade.plugin.version}</version>
                <configuration>
                    <createDependencyReducedPom>false</createDependencyReducedPom>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven.compiler.plugin.version}</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <release>${java.version}</release>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-checkstyle-plugin</artifactId>
                    <version>${maven.checkstyle.plugin.version}</version>
                    <dependencies>
                        <dependency>
                            <groupId>com.bestseller</groupId>
                            <artifactId>bse-checkstyle</artifactId>
                            <version>${bse-checkstyle.version}</version>
                        </dependency>
                    </dependencies>
                    <configuration>
                        <configLocation>com/bestseller/checkstyle.xml</configLocation>
                        <suppressionsLocation>${basedir}/checkstyle-suppressions.xml</suppressionsLocation>
                        <includeTestSourceDirectory>true</includeTestSourceDirectory>
                    </configuration>
                </plugin>
                <!-- For running unit tests -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven.surefire.plugin.version}</version>
                    <configuration>
                        <skipTests>${skipUnitTests}</skipTests>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <dependencies>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-secretsmanager</artifactId>
            <version>${aws-java-sdk.version}</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-lambda-java-core</artifactId>
            <version>${aws-lambda-java-core.version}</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-lambda-java-events</artifactId>
            <version>${aws-lambda-java-events.version}</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>${logback.version}</version>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>${logstash-logback-encoder.version}</version>
        </dependency>

        <dependency>
            <groupId>com.nimbusds</groupId>
            <artifactId>nimbus-jose-jwt</artifactId>
            <version>${nimbus-jwt.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>${httpcore.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson.version}</version>
        </dependency>

        <!-- Test dependencies -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>uk.org.webcompere</groupId>
            <artifactId>system-stubs-core</artifactId>
            <version>${system-stubs.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>uk.org.webcompere</groupId>
            <artifactId>system-stubs-jupiter</artifactId>
            <version>${system-stubs.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
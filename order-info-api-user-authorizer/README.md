# Order Info API User Authorizer

The Order Info API User Authorizer is an AWS lambda which is used to authorize the API user with JWT authentication mechanism. It contains two lambda functions, one for generating JWT and another for validating the JWT. 

[tclink]: https://teamcity.mng.bseint.io/buildConfiguration/Logistics_LogisticLambdas_AccOrderInfoApiOrderInfoApiJwtGeneratorAcc_CommitStageOrderInfoApiOrderInfoApiJwtGeneratorAcc?mode=builds#all-projects
[Build Status for JWT generator lambda][tclink]

[tclink]: https://teamcity.mng.bseint.io/buildConfiguration/Logistics_LogisticLambdas_AccOrderInfoApiOrderInfoApiJwtValidatorAcc_CommitStageOrderInfoApiOrderInfoApiJwtValidatorAcc?mode=builds#all-projects
[Build Status for JWT validator lambda][tclink]

# Contributing

Start by cloning the repository.

The general flow of work is simple:

- Create a branch out of master
- Get a green build (linting, unit tests, code coverage, etc)
- Get two code review approvals
- Ask for the ticket to be merged

# Documentation

Documentation is available in Confluence:
[Order Info API](https://bestseller.jira.com/wiki/spaces/BLD/pages/3695640577/Order+Info+API)
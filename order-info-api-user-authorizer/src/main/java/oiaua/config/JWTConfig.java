package oiaua.config;

public class JWTConfig {
    private static final String PRIVATE_KEY_PREFIX = "-----BEGIN PRIVATE KEY-----";
    private static final String PUBLIC_KEY_PREFIX = "-----BEGIN PUBLIC KEY-----";
    private static final String PRIVATE_KEY_POSTFIX = "-----END PRIVATE KEY-----";
    private static final String PUBLIC_KEY_POSTFIX = "-----END PUBLIC KEY-----";

    private final String issuer = System.getenv("ISSUER");
    private final String subject = System.getenv("SUBJECT");
    private final String audience = System.getenv("AUDIENCE");
    private final String jwtPublicKey = System.getenv("JWT_PUBLIC_KEY");
    private final String jwtPrivateKeySecretName = System.getenv("JWT_PRIVATE_KEY_SECRET_NAME");
    private final String awsRegion = System.getenv("AWS_REGION");
    private final String tokenValidTimeInSeconds = System.getenv("TOKEN_VALID_TIME_IN_SECONDS");

    public String getIssuer() {
        return issuer;
    }

    public String getSubject() {
        return subject;
    }

    public String getAudience() {
        return audience;
    }

    public String getJwtPublicKey() {
        // strip the headers
        String publicKey = jwtPublicKey.replace(PUBLIC_KEY_PREFIX, "").replace(PUBLIC_KEY_POSTFIX, "");
        publicKey = publicKey.replaceAll("\\s+", "");
        return publicKey;
    }

    public String getJwtPrivateKey() {
        String privateKey = SecretConfig.getSecret(jwtPrivateKeySecretName);
        // strip the headers
        privateKey = privateKey.replace(PRIVATE_KEY_PREFIX, "").replace(PRIVATE_KEY_POSTFIX, "");
        privateKey = privateKey.replaceAll("\\s+", "");
        return privateKey;
    }

    public String getAwsRegion() {
        return awsRegion;
    }

    public int getTokenValidTimeInSeconds() {
        return Integer.parseInt(tokenValidTimeInSeconds);
    }
}
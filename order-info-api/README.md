           # Order Info API

Small API to expose order data to 3rd parties, starting with Zendesk.

### What is this repository for? ###

* Exposing order data to Zendesk will improve Customer support agent experience and increase efficiency. Zendesk plugin that Helphouse.io will develop will fetch order data from Bestseller ( acronym to be used in this document BSE) Order API and display the data directly in Zendesk. Thus, customer support agents will have all relevant data available as soon as they open Zendesk ticket for specific orderId.
* Version 0.1

### How do I get set up? ###

* Summary of set up
* Configuration
* Dependencies
* Database configuration
* How to run tests
* Deployment instructions

# Contributing

Start by cloning the repository.

The general flow of work is simple:

- Create a branch out of master
- Get a green build (linting, unit tests, code coverage, etc)
- Get two code review approvals
- Ask for the ticket to be merged

### Additional info ###
* [API documentation](https://bestseller.jira.com/wiki/spaces/BLD/pages/3695640577/Order+Info+API)

### Contact ###

* <EMAIL>
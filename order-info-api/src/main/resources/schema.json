{"$schema": "http://json-schema.org/draft-06/schema#", "$ref": "#/definitions/OrderInfo", "definitions": {"OrderInfo": {"type": "object", "additionalProperties": false, "properties": {"orderId": {"type": "string"}, "dateOrdered": {"type": "string"}, "lastAmended": {"type": "string"}, "checkout": {"type": "string"}, "market": {"type": "string"}, "store": {"type": "string"}, "customerName": {"type": "string"}, "orderMaxStatus": {"type": "string"}, "postCode": {"type": "string"}, "shipments": {"type": "array", "items": {"$ref": "#/definitions/Shipment"}}, "payments": {"type": "array", "items": {"$ref": "#/definitions/Payment"}}, "currency": {"type": "string"}, "orderLines": {"type": "array", "items": {"$ref": "#/definitions/OrderLine"}}, "charges": {"type": "array", "items": {"$ref": "#/definitions/Charge"}}, "grandTotal": {"type": "number"}, "originalTotal": {"type": "number"}}, "required": ["currency", "customerName", "dateOrdered", "grandTotal", "lastAmended", "market", "orderId", "orderLines", "orderMaxStatus", "originalTotal", "store"], "title": "OrderInfo"}, "Charge": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string"}, "description": {"type": "string"}, "pricePaid": {"type": "number"}, "refunded": {"type": "boolean"}}, "required": ["description", "pricePaid", "refunded", "type"], "title": "Charge"}, "OrderLine": {"type": "object", "additionalProperties": false, "properties": {"orderLineNumber": {"type": "integer"}, "ean": {"type": "integer"}, "brand": {"type": "string"}, "lineDescription": {"type": "string"}, "srp": {"type": "string"}, "discount": {"type": "string"}, "pricePaid": {"type": "string"}, "orderLineStatus": {"type": "string"}, "fulfillmentNode": {"type": "string"}}, "required": ["brand", "discount", "ean", "fulfillmentNode", "lineDescription", "orderLineNumber", "orderLineStatus", "pricePaid", "srp"], "title": "OrderLine"}, "Payment": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string"}, "processorId": {"type": "string"}, "subMethod": {"type": "string"}, "subMethodName": {"type": "string"}}, "required": ["processorId", "subMethodName", "type"], "title": "Payment"}, "Shipment": {"type": "object", "additionalProperties": false, "properties": {"partNumber": {"type": "integer"}, "carrier": {"type": "string"}, "trackingNo": {"type": "string"}, "returnShipmentId": {"type": "string"}, "dispatchDate": {"type": "string"}}, "required": ["carrier", "dispatchDate", "partNumber", "returnShipmentId", "trackingNo"], "title": "Shipment"}}}
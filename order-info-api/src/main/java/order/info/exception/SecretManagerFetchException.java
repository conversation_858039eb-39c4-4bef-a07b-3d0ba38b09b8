package order.info.exception;

import com.amazonaws.services.secretsmanager.model.InvalidParameterException;
import com.amazonaws.services.secretsmanager.model.InvalidRequestException;
import com.amazonaws.services.secretsmanager.model.ResourceNotFoundException;

public class SecretManagerFetchException extends RuntimeException{
    public SecretManagerFetchException(String Rxception, ResourceNotFoundException e) {
    }

    public SecretManagerFetchException(String exception) {
    }

    public SecretManagerFetchException(String exception, InvalidRequestException e) {
    }

    public SecretManagerFetchException(String exception, InvalidParameterException e) {
    }
}

package order.info.viewModel;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.util.List;

public record FinancialView(
    BigDecimal grandTotal,
    BigDecimal originalTotal,
    @JsonProperty("payment")
    PaymentView paymentView,
    @JsonProperty("giftCardsPayment")
    GiftCardsPaymentView giftCardsPaymentView,
    @JsonProperty("charges")
    List<ChargeView> chargeViews
) {
}

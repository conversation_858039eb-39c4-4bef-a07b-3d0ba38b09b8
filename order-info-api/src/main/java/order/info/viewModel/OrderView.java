package order.info.viewModel;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.List;

public record OrderView(
    String id,
    ZonedDateTime orderDate,
    ZonedDateTime lastUpdate,
    String checkout,
    String market,
    String store,
    String maxStatus,
    String currency,
    String channelType,
    @JsonProperty("financialsV2")
    FinancialView financials,
    @JsonProperty("shippingAddress")
    ShippingAddressView shippingAddressView,
    List<ShipmentView> shipments,
    @JsonProperty("orderLines")
    OrderLineView orderLinesView
) {
}

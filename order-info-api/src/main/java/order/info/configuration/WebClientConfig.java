package order.info.configuration;


import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;

public final class WebClientConfig {

    private static final int STREAM_BUFFER_SIZE_BYTES = 16 * 1024 * 1024;
    private static final String AUTH_FLOW = "client-credential";
    private static final String OMS_API_URL = System.getenv("OMS_API_URL");

    // Okta configurations
    private static final String OKTA_TOKEN_URL = System.getenv("OKTA_TOKEN_URL");
    private static final String OKTA_CLIENT_ID = System.getenv("OKTA_TOKEN_CLIENT_ID");
    private static final String OKTA_CLIENT_SECRET = SecretConfig.getSecret(System.getenv("OKTA_API_SECRET_TOKEN"));
    private static final String OKTA_SCOPE = System.getenv("OKTA_SCOPE");
    private static final String OKTA_GRANT_TYPE = "client_credentials";
    private static final String BODY_VALUE_TEMPLATE = "grant_type=%s&client_id=%s&client_secret=%s&scope=%s";

    /**
     * Builds a WebClient with default configurations.
     */
    private WebClient.Builder jsonIncreasedBufferWebClientBuilder() {
        return WebClient.builder()
            .defaultHeaders(httpHeaders -> {
                httpHeaders.setContentType(MediaType.APPLICATION_JSON);
                httpHeaders.set("X-Auth-Flow", AUTH_FLOW);
            })
            .codecs(configurer -> configurer
                .defaultCodecs()
                .maxInMemorySize(STREAM_BUFFER_SIZE_BYTES));
    }

    /**
     * Obtains an Okta token using client credentials.
     */
    private OktaToken fetchOktaToken() {
        WebClient webClient = WebClient.builder()
            .defaultHeaders(headers -> headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED))
            .build();

        String bodyValue = String.format(
            BODY_VALUE_TEMPLATE,
            OKTA_GRANT_TYPE, OKTA_CLIENT_ID, OKTA_CLIENT_SECRET, OKTA_SCOPE
        );

        return webClient.post()
            .uri(OKTA_TOKEN_URL)
            .bodyValue(bodyValue)
            .retrieve()
            .bodyToMono(OktaToken.class)
            .block();
    }

    /**
     * Creates a WebClient preconfigured with an Okta bearer token.
     */
    public WebClient omsApiClient() {
        OktaToken oktaToken = fetchOktaToken();

        return jsonIncreasedBufferWebClientBuilder()
            .defaultHeaders(httpHeaders -> httpHeaders.setBearerAuth(oktaToken.accessToken()))
            .baseUrl(OMS_API_URL)
            .build();
    }
}

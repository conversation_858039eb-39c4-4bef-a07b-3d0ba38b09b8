package order.info.configuration;

import com.amazonaws.services.secretsmanager.AWSSecretsManager;
import com.amazonaws.services.secretsmanager.AWSSecretsManagerClientBuilder;
import com.amazonaws.services.secretsmanager.model.*;
import order.info.exception.SecretManagerFetchException;

import java.util.Base64;

public final class SecretConfig {
    private SecretConfig() {
    }

    public static String getSecret(String secretName) {
        AWSSecretsManagerClientBuilder clientBuilder = AWSSecretsManagerClientBuilder.standard();
        AWSSecretsManager client = clientBuilder.build();
        GetSecretValueRequest getSecretValueRequest = new GetSecretValueRequest()
                .withSecretId(secretName);
        GetSecretValueResult getSecretValueResult;
        try {
            getSecretValueResult = client.getSecretValue(getSecretValueRequest);
        } catch (ResourceNotFoundException e) {
            throw new SecretManagerFetchException("The requested secret " + secretName + " was not found", e);
        } catch (InvalidRequestException e) {
            throw new SecretManagerFetchException("The request was invalid due to: " + e.getMessage(), e);
        } catch (InvalidParameterException e) {
            throw new SecretManagerFetchException("The request had invalid params: " + e.getMessage(), e);
        }
        if (getSecretValueResult == null) {
            throw new SecretManagerFetchException("The requested secret " + secretName + " is null");
        }
        // Depending on whether the secret was a string or binary, one of these fields will be populated
        if (getSecretValueResult.getSecretString() != null) {
            return getSecretValueResult.getSecretString();
        } else if (getSecretValueResult.getSecretBinary() != null) {
            return new String(Base64.getDecoder().decode(getSecretValueResult.getSecretBinary().array()));
        } else {
            throw new SecretManagerFetchException("No value returned for secret from AWS Secret Manager!");
        }
    }
}
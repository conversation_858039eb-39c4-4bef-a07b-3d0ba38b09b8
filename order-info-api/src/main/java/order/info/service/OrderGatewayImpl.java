package order.info.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import order.info.viewModel.OrderView;
import org.json.JSONObject;
import org.springframework.core.ParameterizedTypeReference;

import java.util.Map;

import static order.info.converter.OrderMapper.toOrderModel;

public class OrderGatewayImpl implements OrderGateway {
    private static final String QUERY_GET_ORDER_INFO = """
        query getOrderInfo($id: String!) {
            findOrderV2(id: $id) {
                id
                orderDate
                lastUpdate
                checkout
                market
                store
                maxStatus
                currency
                channelType
                financialsV2 {
                    grandTotal
                    originalTotal
                    payment {
                        type
                        subMethodType
                        subMethodId
                        processorId
                        paymentTypeName
                    }
                    giftCardsPayment {
                        giftCards
                    }
                    charges {
                        description
                        amount
                        refunded
                        title
                        type
                    }
                }
                shippingAddress {
                    zipCode
                }
                shipments {
                    index
                    carrier
                    trackingNumber
                    returnTrackingNumber
                    dispatchDate
                }
                 orderLines {
                     items {
                       orderLineNumber
                       items {
                         ean
                         brand
                         description
                         price
                         discount
                         pricePaid
                         status
                         fulfilmentNode
                       }
                     }
                 }
            }
        }
        """;
    private final OmsApiGraphQLService omsApiGraphQLService;

    public OrderGatewayImpl(OmsApiGraphQLService omsApiGraphQLService) {
        this.omsApiGraphQLService = omsApiGraphQLService;
    }

    public JSONObject getOrderInfo(String orderId) throws JsonProcessingException {
        var variables = Map.of("id", orderId);
        OrderView orderView = omsApiGraphQLService.executeQuery(QUERY_GET_ORDER_INFO, variables, "findOrderV2", new ParameterizedTypeReference<>() {
        });
        order.info.model.Order orderModel = toOrderModel(orderView);
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        String jsonString = objectMapper.writeValueAsString(orderModel);
        return new JSONObject(jsonString);
    }
}

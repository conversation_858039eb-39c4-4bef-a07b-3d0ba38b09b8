package order.info.model;

import lombok.Builder;

import java.math.BigDecimal;
import java.util.List;

@Builder
public record Order(
    String orderId,
    String dateOrdered,
    String lastAmended,
    String checkout,
    String market,
    String store,
    String channel,
    String orderMaxStatus,
    String currency,
    BigDecimal grandTotal,
    BigDecimal originalTotal,
    String trackingNumber,
    String postCode,
    List<OrderLine> orderLines,
    List<Charge> charges,
    List<Payment> payments,
    List<Shipment> shipments
) {
}

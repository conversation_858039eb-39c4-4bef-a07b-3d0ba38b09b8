package order.info.converter;

import order.info.model.Charge;
import order.info.model.Order;
import order.info.model.OrderLine;
import order.info.model.Payment;
import order.info.model.Shipment;
import order.info.viewModel.FinancialView;
import order.info.viewModel.OrderLineView;
import order.info.viewModel.OrderView;
import order.info.viewModel.ShipmentView;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class OrderMapper {

    private static final String GIFT_CARD_PAYMENT_METHOD = "GIFTCARD";
    private static final String GIFT_CARD_PROCESSOR_ID = "OPTICARD";
    private static final String GIFT_CARD_SUB_METHOD_ID = "OC_GIFTCARD";
    private static final String GIFT_CARD_PAYMENT_TYPE = "Giftcard payment method";

    public static Order toOrderModel(OrderView orderView) {
        return Order.builder()
            .orderId(orderView.id())
            .dateOrdered(utcToLocalDateTime(orderView.orderDate()))
            .lastAmended(utcToLocalDateTime(orderView.lastUpdate()))
            .checkout(orderView.checkout())
            .market(orderView.market())
            .store(orderView.store())
            .channel(toLowerCaseSafely(orderView.channelType()))
            .orderMaxStatus(orderView.maxStatus())
            .currency(orderView.currency())
            .grandTotal(orderView.financials().grandTotal())
            .originalTotal(orderView.financials().originalTotal())
            .trackingNumber(getTrackingNumbers(orderView.shipments()))
            .postCode(orderView.shippingAddressView().zipCode())
            .orderLines(getOrderLines(orderView.orderLinesView()))
            .charges(getCharges(orderView.financials()))
            .payments(getPayments(orderView.financials()))
            .shipments(getShipments(orderView.shipments()))
            .build();
    }

    private static List<Charge> getCharges(FinancialView financialView) {
        return financialView.chargeViews()
            .stream()
            .map(charge -> Charge.builder()
                .description(charge.description())
                .pricePaid(charge.amount())
                .refunded(charge.refunded())
                .type(charge.type())
                .build(
                )).toList();
    }

    private static List<Payment> getPayments(FinancialView financialView) {
        List<Payment> payments = new ArrayList<>();
        if (financialView.paymentView() != null) {
            payments.add(Payment.builder()
                .type(financialView.paymentView().paymentTypeName())
                .paymentType(financialView.paymentView().type())
                .processorId(financialView.paymentView().processorId())
                .subMethodName(financialView.paymentView().subMethodId())
                .subMethod(financialView.paymentView().subMethodType())
                .build());
        }
        if (financialView.giftCardsPaymentView() != null) {
            payments.add(Payment.builder()
                .type(GIFT_CARD_PAYMENT_METHOD)
                .processorId(GIFT_CARD_PROCESSOR_ID)
                .subMethodName(GIFT_CARD_SUB_METHOD_ID)
                .paymentType(GIFT_CARD_PAYMENT_TYPE)
                .build());
        }
        return payments;
    }

    private static List<Shipment> getShipments(List<order.info.viewModel.ShipmentView> shipments) {
        return shipments.stream()
            .map(shipment -> Shipment.builder()
                .partNumber(shipment.index())
                .carrier(shipment.carrier())
                .trackingNo(shipment.trackingNumber())
                .returnShipmentId(shipment.returnTrackingNumber())
                .dispatchDate(shipment.dispatchDate())
                .build(
                )).toList();
    }

    private static List<OrderLine> getOrderLines(OrderLineView orderLineView) {
        return orderLineView.items()
            .stream()
            .map(item -> item.itemViews()
                .stream()
                .map(item2 -> OrderLine.builder()
                    .orderLineNumber(item.orderLineNumber())
                    .ean(item2.ean())
                    .brand(item2.brand())
                    .lineDescription(item2.description())
                    .pricePaid(item2.pricePaid())
                    .srp(item2.price())
                    .discount(item2.discount())
                    .orderLineStatus(item2.status())
                    .fulfillmentNode(item2.fulfilmentNode())
                    .build()).toList())
            .toList()
            .stream()
            .flatMap(List::stream)
            .toList();
    }

    private static String getTrackingNumbers(List<ShipmentView> shipments) {
        return shipments.stream()
            .map(ShipmentView::trackingNumber)
            .collect(Collectors.joining(","));
    }

    private static String toLowerCaseSafely(String value) {
        return value == null ? null : value.toLowerCase();
    }

    private static String utcToLocalDateTime(ZonedDateTime utcDateTime) {
        ZonedDateTime localDateTime = utcDateTime.withZoneSameInstant(ZoneId.of("Europe/Amsterdam"));
        return localDateTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
    }

}



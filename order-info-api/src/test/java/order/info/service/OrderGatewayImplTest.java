package order.info.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import order.info.model.Order;
import order.info.viewModel.OrderView;
import order.info.viewModel.FinancialView;
import order.info.viewModel.GiftCardsPaymentView;
import order.info.viewModel.ChargeView;
import order.info.viewModel.ShippingAddressView;
import order.info.viewModel.ShipmentView;
import order.info.viewModel.OrderLineView;
import order.info.viewModel.OrderLineItemView;
import order.info.viewModel.ItemView;
import order.info.viewModel.PaymentView;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.ParameterizedTypeReference;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.anyString;

@ExtendWith(MockitoExtension.class)
public class OrderGatewayImplTest {
    @Mock
    private OmsApiGraphQLService omsApiGraphQLService;

    @InjectMocks
    private OrderGatewayImpl orderGatewayImpl;

    @Test
    public void getOrderInfo_givenOrderId_returnOrderInfo() throws JsonProcessingException {
        // Arrange
        String orderId = "12345";
        OrderView orderView = new OrderView(
            "1",
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            "checkout",
            "market",
            "store",
            "maxStatus",
            "currency",
            "STOREFRONT",
            new FinancialView(
                BigDecimal.TEN,
                BigDecimal.ONE,
                new PaymentView("type", "paymentType", "processorId", "subMethodName", "subMethodType"),
                new GiftCardsPaymentView(List.of("GiftCardNumber")),
                List.of(new ChargeView("description", BigDecimal.TEN, false, "title", "type"))),
            new ShippingAddressView("zipCode"),
            List.of(new ShipmentView(1, "carrier", "trackingNumber", "returnTrackingNumber", LocalDateTime.now())),
            new OrderLineView(List.of(new OrderLineItemView(1, List.of(new ItemView("ean", "brand", "description", BigDecimal.ONE, BigDecimal.ONE, BigDecimal.ONE, "status", "fulfilmentNode")))))
        );

        when(omsApiGraphQLService.executeQuery(anyString(), any(Map.class), eq("findOrderV2"), any(ParameterizedTypeReference.class)))
            .thenReturn(orderView);

        // Act
        orderGatewayImpl.getOrderInfo(orderId);

        // Assert
        verify(omsApiGraphQLService).executeQuery(anyString(), any(Map.class), eq("findOrderV2"), any(ParameterizedTypeReference.class));
    }
}

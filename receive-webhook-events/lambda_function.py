"""
Stores Fulfillment Tools order updates in Kafka
"""
import logging
from kafka import KafkaProducer
import json
from json import JSONDecodeError
from pprint import pformat
import os
import json_log_formatter

log = logging.getLogger(__name__)

# to update the formatter of the logger that used by AWS lambda...
# Without this(adding new log handler) the log message is duplicated in DD.
root_logger = logging.getLogger()
if bool(os.getenv("USE_DATADOG_LOGGER")) and len(root_logger.handlers) > 0:
    root_logger.handlers[0].setFormatter(json_log_formatter.VerboseJSONFormatter())

log.setLevel(logging.INFO)


KAFKA_HOSTS = os.environ.get("KAFKA_BROKER_LIST", "localhost")
TOPIC = os.environ.get("KAFKA_TOPIC")


def response(status, message):
    return {
        "statusCode": status,
        "body": json.dumps({"message": message}),
        "isBase64Encoded": False,
        "headers": {"content-type": "application/json"},
    }


def lambda_handler(event, context):
    """
    Entry point function
    :param event: Event data passed in by AWS
    :param context: Event context
    """
    # remove sensitive fields
    del event["headers"]["Authorization"]
    del event["multiValueHeaders"]["Authorization"]

    log.debug("Event (redacted):\n%s", pformat(event))
    log.debug("Context: %s", context)

    principal = event["requestContext"]["authorizer"]["principalId"]
    assert event["httpMethod"] == "POST", f"{event} should be POST"

    try:
        body = event["body"]
    except JSONDecodeError as ex:
        log.exception("Can't decode JSON:\n%s", event["body"])
        return response(400, f"Can't decode JSON: {ex}")

    try:
        log.debug("Producing message:\n%s", pformat(body))
        produce_messages(body, context)
    except:
        log.exception("Can't produce message in Kafka")
        return response(500, "Unexpected error occurred")

    return response(200, "Event successfully received")


def produce_messages(message, context):
    producer = KafkaProducer(
        client_id=context.function_name,
        bootstrap_servers=KAFKA_HOSTS,
    )

    try:
        log.debug("Producing to %s: %s", TOPIC, message)
        result = producer.send(TOPIC, message.encode("UTF-8"))
        log.info("Result: %s", result.get())
    finally:
        producer.close()


if __name__ == "__main__":
    from collections import namedtuple
    from sys import stdin, argv

    LambdaContext = namedtuple("LambdaContext", ("function_name",))

    # input
    request = stdin.read()

    # process
    event = {
        "headers": {"Authorization": ""},
        "multiValueHeaders": {"Authorization": ""},
        "httpMethod": "POST",
        "requestContext": {"authorizer": {"principalId": "fulfillmenttools"}},
        "body": request,
    }
    context = LambdaContext(function_name=__file__)
    response = lambda_handler(event, context)

    # output
    print("HTTP", response["statusCode"], "\n")
    print(response["body"])

#!/bin/bash -eux

set -o pipefail

# Set default artifact name
: "${ARTIFACT_NAME:=$(basename "$(dirname "$0")")/artifact.zip}"

# Resolve absolute path
ARTIFACT_NAME="$(readlink -m "$ARTIFACT_NAME")"

# Change to the project sub-directory
pushd "$(dirname "$0")"

venv() {
  echo Creating virtual environment
  # work within a virtual environment
  # because we may not have enough permissions in the default $PYTHONUSERBASE
  # for example, if we're running in a Docker container and we're not root
  python -m venv ./venv
  . ./venv/bin/activate

  pip --version
  python --version
}

deps() {
  echo Installing dependencies
  pip install -r requirements.txt
}

test() {
  echo Running doc tests
  python -m doctest *.py

  echo Running unit tests
  python -m unittest
}

package() {
  echo Packaging artifact "$ARTIFACT_NAME"

  local -a deps=($(cat requirements.txt))

  local -a dep_files=($(
    pip show -f ${deps[*]%%==*} |
      awk '
        # Parse site packages dir
        /Location:/ {
          LOC=$2
        }

	# Parse "Files:" section
	# excluding paths starting with ../
	(/  / && !/  [.][.]/) {
	  # Strip up to parent
	  # `zipfile` will handle nested files and dirs
	  # with relative paths preserved
          sub("/.*", "")

          # Form full path
          print LOC "/" $1
        }
      ' |
      uniq
  ))

  python -m zipfile -c "$ARTIFACT_NAME" lambda_function.py "${dep_files[@]}"
}

venv
deps
test
package

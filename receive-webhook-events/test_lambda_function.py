import unittest
from unittest.mock import MagicMock, patch
import json
from lambda_function import J<PERSON><PERSON>ecodeError

import lambda_function


class BytesContainingJsonValue:
    def __init__(self, value):
        self.value = value

    def __repr__(self):
        return f"json.dumps({self.value}).encode('UTF-8')"

    def __eq__(self, other):
        return self.value == json.loads(other.decode("UTF-8"))


class TestLambdaFunction(unittest.TestCase):
    def _make_event(self, body):
        return {
            "headers": {"Authorization": ""},
            "multiValueHeaders": {"Authorization": ""},
            "httpMethod": "POST",
            "queryStringParameters": None,
            "requestContext": {
                "authorizer": {"principalId": "fulfillmenttools"},
                "httpMethod": "POST",
            },
            "body": body,
            "isBase64Encoded": False,
        }

    def _make_context(self):
        return MagicMock(function_name="my-fn")

    def reformat_json_bytes(self, content):
        return json.dumps(json.loads(content.decode("UTF-8"))).encode("UTF-8")

    with open("example.json", "rb") as f:
        valid_event_bytes = f.read()

    @patch("lambda_function.KAFKA_HOSTS", "kafka-1.bseint.io,kafka-2.bseint.io")
    @patch("lambda_function.TOPIC", "myTopic")
    @patch("lambda_function.KafkaProducer")
    def test_lambda_handler(self, Producer):
        # arrange
        event_str = TestLambdaFunction.valid_event_bytes.decode("UTF-8")
        fft_event = json.loads(event_str)

        event = self._make_event(event_str)
        context = self._make_context()

        # act
        response = lambda_function.lambda_handler(event, context)

        # assert
        Producer.assert_called_with(
            client_id=context.function_name,
            bootstrap_servers="kafka-1.bseint.io,kafka-2.bseint.io",
        )
        Producer.return_value.send.assert_called_with(
            "myTopic",
            BytesContainingJsonValue(
                {"event": "PICK_JOB_PICKING_FINISHED", **fft_event}
            ),
        )
        Producer.return_value.send.return_value.get.assert_called()

        self.assertEqual(response["statusCode"], 200)
        self.assertRegex(
            response["body"], "Event successfully received"
        )
        self.assertEqual(response["isBase64Encoded"], False)
        headers = response["headers"]
        self.assertIn("content-type", headers)
        self.assertEqual(headers["content-type"], "application/json")

    @patch("lambda_function.KafkaProducer")
    def test_lambda_handler_send_failure(self, Producer):
        # arrange
        event = self._make_event(TestLambdaFunction.valid_event_bytes)
        context = self._make_context()

        def fail_send():
            raise Exception("Kafka is sick")

        Producer.return_value.send.return_value.get.side_effect = fail_send

        # act
        response = lambda_function.lambda_handler(event, context)

        # assert
        self.assertEqual(response["statusCode"], 500)
        self.assertEqual(response["isBase64Encoded"], False)
        headers = response["headers"]
        self.assertIn("content-type", headers)
        self.assertEqual(headers["content-type"], "application/json")

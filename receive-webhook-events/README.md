# Receive Fulfillment Tools Events

Function accepts a call from Fulfillment Tools REST API and stores it in Kafka.


## Prerequisites

Python 3.9


## Setting up local environment

1. Create virtual environment

        python -m venv ./venv

2. Activate virtual environment in the current shell

        . ./venv/bin/activate

3. Install dependencies

        pip install -r requirements.txt


## Running tests

1. Run unit tests

        python -m unittest

2. Reformat the code

        pip install black
        python -m black *.py


## Invoking function locally

Run function module as a script with request payload fed to the standard input

    python -m lambda_function <<<'
      {
        "event": "PICK_JOB_PICKING_FINISHED",
        "payload": {
          "dummy": "X"
        },
        "eventId": "5848896837094738"
      }
    '
